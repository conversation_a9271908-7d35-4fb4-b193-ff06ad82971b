"""
Retroactive Vouch Scanner
Scans Discord channel for vouch messages with image attachments from the last 3 days
and awards points using the existing vouch tracking system.
"""

import discord
import asyncio
import os
from dotenv import load_dotenv
import datetime
from typing import Dict, List, Tuple
import logging
from themethodbot.vouch_tracking import add_vouch_point

# Load environment variables
load_dotenv()

# Configuration
DISCORD_BOT_TOKEN = os.getenv("DISCORD_BOT_TOKEN")
VOUCH_CHANNEL_ID = 1340392172691918848  # Actual vouch channel ID from vouch_tracking.py
HOURS_BACK = 72  # 3 days

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('retroactive_vouch_scan.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('retroactive_vouch_scanner')

class VouchScanResults:
    """Class to track scanning results."""
    
    def __init__(self):
        self.total_messages_processed = 0
        self.total_images_found = 0
        self.total_points_awarded = 0
        self.user_breakdown: Dict[str, Dict[str, int]] = {}
        self.errors: List[str] = []
    
    def add_user_data(self, user_id: int, username: str, images_count: int, points_awarded: int):
        """Add user data to the breakdown."""
        user_key = f"{username} ({user_id})"
        if user_key not in self.user_breakdown:
            self.user_breakdown[user_key] = {
                'images': 0,
                'points': 0
            }
        
        self.user_breakdown[user_key]['images'] += images_count
        self.user_breakdown[user_key]['points'] += points_awarded
        self.total_images_found += images_count
        self.total_points_awarded += points_awarded
    
    def add_error(self, error_msg: str):
        """Add an error to the error list."""
        self.errors.append(error_msg)
        logger.error(error_msg)
    
    def print_summary(self):
        """Print a comprehensive summary of the scan results."""
        print("\n" + "="*60)
        print("🔍 RETROACTIVE VOUCH SCAN RESULTS")
        print("="*60)
        
        print(f"\n📊 SUMMARY STATISTICS:")
        print(f"   • Total messages processed: {self.total_messages_processed}")
        print(f"   • Total images found: {self.total_images_found}")
        print(f"   • Total points awarded: {self.total_points_awarded}")
        print(f"   • Users who received points: {len(self.user_breakdown)}")
        
        if self.user_breakdown:
            print(f"\n👥 USER BREAKDOWN:")
            # Sort users by points awarded (descending)
            sorted_users = sorted(
                self.user_breakdown.items(), 
                key=lambda x: x[1]['points'], 
                reverse=True
            )
            
            for user, data in sorted_users:
                print(f"   • {user}")
                print(f"     - Images found: {data['images']}")
                print(f"     - Points awarded: {data['points']}")
        
        if self.errors:
            print(f"\n⚠️  ERRORS ENCOUNTERED ({len(self.errors)}):")
            for i, error in enumerate(self.errors, 1):
                print(f"   {i}. {error}")
        
        print("\n" + "="*60)
        print("✅ Scan completed successfully!")
        print("="*60)

async def scan_vouch_channel():
    """Main function to scan the vouch channel and award retroactive points."""
    # Set up bot intents
    intents = discord.Intents.default()
    intents.messages = True
    intents.guilds = True
    intents.message_content = True
    
    # Create bot client
    client = discord.Client(intents=intents)
    results = VouchScanResults()
    
    @client.event
    async def on_ready():
        logger.info(f"✅ Logged in as {client.user}")
        
        try:
            # Get the vouch channel
            channel = client.get_channel(VOUCH_CHANNEL_ID)
            if not channel:
                error_msg = f"❌ Could not find channel with ID {VOUCH_CHANNEL_ID}"
                results.add_error(error_msg)
                await client.close()
                return
            
            logger.info(f"📍 Found channel: #{channel.name}")
            
            # Calculate cutoff time (3 days ago)
            cutoff_time = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(hours=HOURS_BACK)
            logger.info(f"🕒 Scanning messages from {cutoff_time.strftime('%Y-%m-%d %H:%M:%S UTC')} onwards")
            
            # Scan messages
            logger.info("🔍 Starting message scan...")
            
            async for message in channel.history(limit=None, after=cutoff_time):
                try:
                    results.total_messages_processed += 1
                    
                    # Skip bot messages
                    if message.author.bot:
                        continue
                    
                    # Check for image attachments
                    image_attachments = [
                        att for att in message.attachments 
                        if att.content_type and att.content_type.startswith('image/')
                    ]
                    
                    if not image_attachments:
                        continue
                    
                    # Award points for each image
                    images_count = len(image_attachments)
                    points_awarded = 0
                    
                    for _ in image_attachments:
                        try:
                            # Use existing vouch tracking system to add points
                            new_total = add_vouch_point(message.author.id)
                            points_awarded += 1
                            logger.info(f"✅ Awarded point to {message.author.name} (ID: {message.author.id}). New total: {new_total}")
                        except Exception as e:
                            error_msg = f"Failed to award point to {message.author.name} (ID: {message.author.id}): {str(e)}"
                            results.add_error(error_msg)
                    
                    # Add to results
                    if points_awarded > 0:
                        results.add_user_data(
                            message.author.id,
                            message.author.name,
                            images_count,
                            points_awarded
                        )
                        
                        logger.info(f"📸 Processed message from {message.author.name}: {images_count} images, {points_awarded} points awarded")
                
                except Exception as e:
                    error_msg = f"Error processing message {message.id}: {str(e)}"
                    results.add_error(error_msg)
            
            logger.info("✅ Message scan completed")
            
        except Exception as e:
            error_msg = f"Critical error during scan: {str(e)}"
            results.add_error(error_msg)
        
        finally:
            # Print results and close
            results.print_summary()
            await client.close()
    
    # Run the bot
    try:
        await client.start(DISCORD_BOT_TOKEN)
    except Exception as e:
        logger.error(f"Failed to start bot: {e}")
        results.add_error(f"Failed to start bot: {str(e)}")
        results.print_summary()

if __name__ == "__main__":
    print("🚀 Starting retroactive vouch scanner...")
    print(f"📍 Target channel ID: {VOUCH_CHANNEL_ID}")
    print(f"⏰ Scanning last {HOURS_BACK} hours ({HOURS_BACK//24} days)")
    print("🔍 Looking for messages with image attachments...")
    print("\nStarting scan...\n")
    
    asyncio.run(scan_vouch_channel())
