import discord
from discord.ext import commands
from discord import app_commands
import logging
import os
from dotenv import load_dotenv
import re
import asyncio
import traceback
import time
import aiohttp
import ssl
import json
import datetime
from typing import Dict, Optional, Any, List
import psutil

import sys
import os

# Import from themethodbot common folder
from themethodbot.common.bot import (
    cerv,
    nelo,
    Glitchyz,
    track,
    extract_group_link
)

# Import our payment app module
from themethodbot.paymentapp import PaymentMethodButtons, PaymentMethodSelector, setup_payment_views



from themethodbot.common.bot import (
    process_cart_items,
    calculate_fees,
    create_locked_order_embed,

    fetch_order_details,
    track_order_status,
    open_store,
    close_store,
    vouchtop
)
from themethodbot.common.check_group_order import process_group_order

# Import PriceCheckerV2 for advanced pricing analysis
from .pricecheckerv2 import method_bot_price_analysis

# Import our custom embed templates
from themethodbot.embed_templates import (
    create_error_embed,
    create_processing_embed,
    create_order_summary_embed,
    create_loyal_customer_embed
)

# Import the automated order system
from botorders import setup_order_commands

# Import the vouch tracking system
import themethodbot.vouch_tracking as vouch_tracking

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('themethodbot.log', encoding='utf-8')
    ]
)

# Create a logger for this module
logger = logging.getLogger('themethodbot')

# Get environment variables
DISCORD_BOT_TOKEN = os.getenv("DISCORD_BOT_TOKEN")
DISCORD_GUILD_ID = int(os.getenv("DISCORD_GUILD_ID"))
TOKEN_2 = os.getenv("TOKEN_2")  # Add TOKEN_2 for channel operations
COMMANDS_GLOBAL = False

# Set up bot
intents = discord.Intents.default()
intents.messages = True
intents.guilds = True
intents.message_content = True
intents.dm_messages = True

# Create the bot with optimized settings
bot = commands.Bot(
    command_prefix="!",
    intents=intents,
    dm_permission=True,
    case_insensitive=True,  # Make commands case-insensitive
    max_messages=10000,     # Increase message cache for better performance
    heartbeat_timeout=150.0 # Increase heartbeat timeout for stability
)

GUILD = discord.Object(id=DISCORD_GUILD_ID)

# Global session for HTTP requests
_http_session: Optional[aiohttp.ClientSession] = None

# Performance metrics
command_metrics: Dict[str, Dict[str, float]] = {}

# Cache for frequently accessed data
data_cache: Dict[str, Any] = {}

# Path to the tracking data file
TRACKING_DATA_FILE = 'tracking_data.json'



# Path to the status embed data file
STATUS_EMBED_DATA_FILE = 'status_embed_data.json'

# Path to the credits data files
CREDITS_DATA_FILE = 'credits_data.json'
REDEEMED_KEYS_FILE = 'redeemed_keys.json'
CERVPAY_DATA_FILE = 'cervpay_data.json'

# Active tracking information
active_tracking: Dict[str, Dict[str, Any]] = {}



# Status embed data storage
status_embed_data: Dict[str, Any] = {}

# Credits system data storage
user_credits: Dict[str, float] = {}  # user_id -> credit_balance
redeemed_keys: Dict[str, Dict[str, Any]] = {}  # key -> {user_id, amount, timestamp}

# CervPay system data storage
cervpay_enabled: bool = False  # Global toggle for automated payment system

# Credits system logging channel ID
CREDITS_LOG_CHANNEL_ID = 1389728392601010300

async def log_credits_event(event_type: str, user: discord.Member, admin: discord.Member = None, amount: float = None, serial_key: str = None, balance: float = None, error: str = None):
    """Log credits system events to the dedicated logging channel."""
    try:
        # Skip logging for balance check events
        if event_type == "balance_check":
            return

        # Get the logging channel
        log_channel = bot.get_channel(CREDITS_LOG_CHANNEL_ID)
        if not log_channel:
            logger.error(f"Credits logging channel {CREDITS_LOG_CHANNEL_ID} not found")
            return

        # Create embed based on event type
        if event_type == "redemption":
            embed = discord.Embed(
                title="💳 Credit Redemption",
                color=discord.Color.from_rgb(87, 242, 135)  # Green
            )
            embed.add_field(name="User", value=f"{user.mention} ({user.display_name})", inline=True)
            embed.add_field(name="User ID", value=str(user.id), inline=True)
            embed.add_field(name="Amount", value=f"${amount:.2f}", inline=True)
            embed.add_field(name="Serial Key", value=f"`{serial_key}`", inline=True)
            embed.add_field(name="New Balance", value=f"${balance:.2f}", inline=True)

        elif event_type == "credit_removal":
            embed = discord.Embed(
                title="🗑️ Credit Removal",
                color=discord.Color.from_rgb(237, 66, 69)  # Red
            )
            embed.add_field(name="Admin", value=f"{admin.mention} ({admin.display_name})", inline=True)
            embed.add_field(name="Target User", value=f"{user.mention} ({user.display_name})", inline=True)
            embed.add_field(name="Amount Removed", value=f"${amount:.2f}", inline=True)
            embed.add_field(name="New Balance", value=f"${balance:.2f}", inline=True)

        elif event_type == "error":
            embed = discord.Embed(
                title="⚠️ Credits System Error",
                color=discord.Color.from_rgb(255, 165, 0)  # Orange
            )
            embed.add_field(name="User", value=f"{user.mention} ({user.display_name})", inline=True)
            embed.add_field(name="Error", value=error[:1000], inline=False)  # Limit error length

        # Add timestamp
        embed.timestamp = datetime.datetime.utcnow()
        embed.set_footer(text="Credits System Log")

        # Send to logging channel
        await log_channel.send(embed=embed)

    except Exception as e:
        logger.error(f"Error logging credits event: {e}")

# Queue system constants
TICKET_CATEGORY_ID = 1340194718637625395  # Category ID for tickets
QUEUE_CATEGORY_ID = 1389060752832200745  # Category ID for queue (updated to correct ID)
DELIVERING_CATEGORY_ID = 1354242418211422419  # Category ID for delivering

# Subtotal validation constants
MIN_SUBTOTAL = 23.0  # Minimum subtotal for queue eligibility
MAX_SUBTOTAL = 35.0  # Maximum subtotal for queue eligibility

# Status message ID to update
STATUS_MESSAGE_ID = 1365395438542262345



# Function to save status embed data
async def save_status_embed_data():
    """Save status embed data to file."""
    try:
        with open(STATUS_EMBED_DATA_FILE, 'w') as f:
            json.dump(status_embed_data, f)
        logger.info(f"Saved status embed data: {status_embed_data}")
    except Exception as e:
        logger.error(f"Error saving status embed data: {e}")

# Function to load status embed data
async def load_status_embed_data():
    """Load status embed data from file."""
    global status_embed_data
    try:
        if os.path.exists(STATUS_EMBED_DATA_FILE):
            with open(STATUS_EMBED_DATA_FILE, 'r') as f:
                status_embed_data = json.load(f)
            logger.info(f"Loaded status embed data: {status_embed_data}")
        else:
            # Initialize with default message ID if file doesn't exist
            status_embed_data = {'message_id': 1388974034066214993}
            await save_status_embed_data()
            logger.info("Initialized status embed data with default message ID")
    except Exception as e:
        logger.error(f"Error loading status embed data: {e}")
        # Fallback to default
        status_embed_data = {'message_id': 1388974034066214993}

# Function to save credits data
async def save_credits_data():
    """Save user credits data to file."""
    try:
        with open(CREDITS_DATA_FILE, 'w') as f:
            json.dump(user_credits, f, indent=2)
        logger.info(f"Saved credits data for {len(user_credits)} users")
    except Exception as e:
        logger.error(f"Error saving credits data: {e}")

# Function to load credits data
async def load_credits_data():
    """Load user credits data from file."""
    global user_credits
    try:
        if os.path.exists(CREDITS_DATA_FILE):
            with open(CREDITS_DATA_FILE, 'r') as f:
                user_credits = json.load(f)
            logger.info(f"Loaded credits data for {len(user_credits)} users")
        else:
            user_credits = {}
            logger.info("Initialized empty credits data")
    except Exception as e:
        logger.error(f"Error loading credits data: {e}")
        user_credits = {}

# Function to save redeemed keys data
async def save_redeemed_keys():
    """Save redeemed keys data to file."""
    try:
        with open(REDEEMED_KEYS_FILE, 'w') as f:
            json.dump(redeemed_keys, f, indent=2)
        logger.info(f"Saved redeemed keys data for {len(redeemed_keys)} keys")
    except Exception as e:
        logger.error(f"Error saving redeemed keys data: {e}")

# Function to load redeemed keys data
async def load_redeemed_keys():
    """Load redeemed keys data from file."""
    global redeemed_keys
    try:
        if os.path.exists(REDEEMED_KEYS_FILE):
            with open(REDEEMED_KEYS_FILE, 'r') as f:
                redeemed_keys = json.load(f)
            logger.info(f"Loaded redeemed keys data for {len(redeemed_keys)} keys")
        else:
            redeemed_keys = {}
            logger.info("Initialized empty redeemed keys data")
    except Exception as e:
        logger.error(f"Error loading redeemed keys data: {e}")
        redeemed_keys = {}

# Function to save CervPay data
async def save_cervpay_data():
    """Save CervPay toggle state to file."""
    try:
        with open(CERVPAY_DATA_FILE, 'w') as f:
            json.dump({'enabled': cervpay_enabled}, f, indent=2)
        logger.info(f"Saved CervPay data: enabled={cervpay_enabled}")
    except Exception as e:
        logger.error(f"Error saving CervPay data: {e}")

# Function to load CervPay data
async def load_cervpay_data():
    """Load CervPay toggle state from file."""
    global cervpay_enabled
    try:
        if os.path.exists(CERVPAY_DATA_FILE):
            with open(CERVPAY_DATA_FILE, 'r') as f:
                data = json.load(f)
                cervpay_enabled = data.get('enabled', False)
            logger.info(f"Loaded CervPay data: enabled={cervpay_enabled}")
        else:
            cervpay_enabled = False
            logger.info("Initialized CervPay as disabled")
    except Exception as e:
        logger.error(f"Error loading CervPay data: {e}")
        cervpay_enabled = False

# Credits system helper functions
def load_serial_keys(denomination: int) -> set:
    """Load serial keys for a specific denomination from file."""
    filename = f"{denomination}keys.txt"
    filepath = os.path.join(os.path.dirname(__file__), '..', filename)

    try:
        with open(filepath, 'r') as f:
            keys = {line.strip() for line in f if line.strip()}
        logger.info(f"Loaded {len(keys)} serial keys for ${denomination}")
        return keys
    except FileNotFoundError:
        logger.error(f"Serial keys file not found: {filename}")
        return set()
    except Exception as e:
        logger.error(f"Error loading serial keys from {filename}: {e}")
        return set()

def validate_serial_key(key: str) -> Optional[int]:
    """Validate a serial key and return the credit amount if valid."""
    key = key.strip().upper()

    # Check each denomination
    for amount in [3, 5, 10, 20]:
        valid_keys = load_serial_keys(amount)
        if key in valid_keys:
            return amount

    return None

def format_currency(amount: float) -> str:
    """Format amount as currency string."""
    return f"${amount:.2f}"

async def add_user_credits(user_id: str, amount: float) -> float:
    """Add credits to a user's balance and return new total."""
    current_balance = user_credits.get(user_id, 0.0)
    new_balance = current_balance + amount
    user_credits[user_id] = new_balance
    await save_credits_data()
    return new_balance

async def get_user_credits(user_id: str) -> float:
    """Get a user's credit balance."""
    return user_credits.get(user_id, 0.0)

async def is_key_redeemed(key: str) -> bool:
    """Check if a serial key has already been redeemed."""
    return key.upper() in redeemed_keys

async def mark_key_redeemed(key: str, user_id: str, amount: int) -> None:
    """Mark a serial key as redeemed."""
    redeemed_keys[key.upper()] = {
        'user_id': user_id,
        'amount': amount,
        'timestamp': datetime.datetime.now().isoformat()
    }
    await save_redeemed_keys()

# Function to update the status message with clocked-in staff
async def update_status_message(guild):
    """Update the status message with the current clocked-in staff."""
    try:
        # Get the status channel
        status_channel = guild.get_channel(1340406893927075900)  # Status Channel
        if not status_channel:
            logger.error("Status channel not found")
            return False

        # Get the status message
        try:
            status_message = await status_channel.fetch_message(STATUS_MESSAGE_ID)
        except discord.NotFound:
            logger.error(f"Status message with ID {STATUS_MESSAGE_ID} not found")
            return False
        except Exception as e:
            logger.error(f"Error fetching status message: {e}")
            return False

        # Check if the store is open
        store_open = await is_store_open(guild)



        # Create the appropriate embed based on store status
        if store_open:
            embed = discord.Embed(
                title="🚀 The Method is Now Open!",
                description="**We are now accepting orders!** Place your order using the instructions below.",
                color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
            )

            # Add some spacing between title and content
            embed.description += "\n"

            # Add information about how to order
            embed.add_field(
                name="📋 How to Order",
                value=f"Check out <#{1340210714891128882}> for detailed instructions on how to place an order.",
                inline=False
            )

            # Add information about payment methods
            embed.add_field(
                name="💳 Payment Methods",
                value="We accept various payment methods including PayPal, Venmo, and Cash App.",
                inline=False
            )



            # Set image and footer
            embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1344614541195218975/a.jpg?ex=67c18d60&is=67c03be0&hm=31b0756c3bb9d314fecab34039c85025b4271a349a8d5f9a7267a4c74e9864bc&=&format=webp&width=947&height=541")
            embed.set_footer(text="The Method | Fast & Reliable Service")
            embed.timestamp = datetime.datetime.now()

        else:
            embed = discord.Embed(
                title="🔴 The Method is Now Closed!",
                description="**We are currently closed.** Please check back later for updates.",
                color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
            )

            # Add some spacing between title and content
            embed.description += "\n"

            # Set image and footer
            embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1344614540758749245/ClosedBanner.jpg?ex=67fa3de0&is=67f8ec60&hm=b74d7be3b974fc13fa60f06fa6f241dc9f02816a50ac51dff4809dbfa5da0ecb&=&format=webp&width=1466&height=838")
            embed.set_footer(text="The Method | Currently Closed")
            embed.timestamp = datetime.datetime.now()

        # Update the message
        await status_message.edit(embed=embed)
        logger.info(f"Updated status message with {len(staff_mentions)} clocked-in staff members")
        return True

    except Exception as e:
        logger.error(f"Error updating status message: {e}")
        logger.error(traceback.format_exc())
        return False

# Function to get queue position
async def get_queue_position(channel):
    """
    Get the position of a channel in the queue category.

    Args:
        channel: The Discord channel to check

    Returns:
        int or None: Position in queue (1-based) if channel is in queue, None otherwise
    """
    try:
        # Check if channel is in the queue category
        if channel.category_id != QUEUE_CATEGORY_ID:
            return None

        # Get the queue category
        queue_category = channel.guild.get_channel(QUEUE_CATEGORY_ID)
        if not queue_category:
            logger.error(f"Queue category with ID {QUEUE_CATEGORY_ID} not found")
            return None

        # Get all channels in the queue category, sorted by position
        queue_channels = sorted(queue_category.channels, key=lambda c: c.position)

        # Find the position of the current channel (1-based)
        for i, queue_channel in enumerate(queue_channels):
            if queue_channel.id == channel.id:
                return i + 1

        return None
    except Exception as e:
        logger.error(f"Error getting queue position: {e}")
        return None

async def get_total_queue_count():
    """
    Get the total number of channels in the queue category.

    Returns:
        int: Total number of channels in queue
    """
    try:
        # Get the queue category
        queue_category = bot.get_channel(QUEUE_CATEGORY_ID)
        if not queue_category:
            logger.error(f"Queue category with ID {QUEUE_CATEGORY_ID} not found")
            return 0

        return len(queue_category.channels)
    except Exception as e:
        logger.error(f"Error getting total queue count: {e}")
        return 0

# Function to move a channel to the queue category
async def move_to_queue(channel, position=None):
    """Move a channel to the queue category.

    Args:
        channel: The channel to move
        position: Optional position in the category (default: None, which appends to the end)
    """
    try:
        # Get the queue category
        queue_category = channel.guild.get_channel(QUEUE_CATEGORY_ID)
        if not queue_category:
            logger.error(f"Queue category with ID {QUEUE_CATEGORY_ID} not found")
            return False

        # Validate position parameter to prevent TypeError
        if position is not None and not isinstance(position, int):
            logger.warning(f"Invalid position parameter: {position} (type: {type(position)}). Using None instead.")
            position = None

        # Move the channel to the queue category
        # Only pass position parameter if it's a valid integer
        if position is not None:
            await channel.edit(category=queue_category, position=position)
        else:
            await channel.edit(category=queue_category)

        # Get the number of channels in the queue for logging
        queue_position = len(queue_category.channels)

        logger.info(f"Moved channel {channel.name} to queue at position {queue_position}")
        return True
    except Exception as e:
        logger.error(f"Error moving channel to queue: {e}")
        logger.error(traceback.format_exc())
        return False

# Function to move a channel to the delivering category
async def move_to_delivering(channel, position=None):
    """Move a channel to the delivering category.

    Args:
        channel: The channel to move
        position: Optional position in the category (default: None, which appends to the end)
    """
    try:
        # Get the delivering category
        delivering_category = channel.guild.get_channel(DELIVERING_CATEGORY_ID)
        if not delivering_category:
            logger.error(f"Delivering category with ID {DELIVERING_CATEGORY_ID} not found")
            return False

        # Validate position parameter to prevent TypeError
        if position is not None and not isinstance(position, int):
            logger.warning(f"Invalid position parameter: {position} (type: {type(position)}). Using None instead.")
            position = None

        # Move the channel to the delivering category
        # Only pass position parameter if it's a valid integer
        if position is not None:
            await channel.edit(category=delivering_category, position=position)
        else:
            await channel.edit(category=delivering_category)

        logger.info(f"Moved channel {channel.name} to delivering category")
        return True
    except Exception as e:
        logger.error(f"Error moving channel to delivering: {e}")
        logger.error(traceback.format_exc())
        return False

# Function to move a channel to the track validation category
async def move_to_track_validation(channel, position=None):
    """Move a channel to the track validation category (1361051057475682537).

    Args:
        channel: The channel to move
        position: Optional position in the category (default: None, which appends to the end)
    """
    try:
        # Track validation category ID
        TRACK_VALIDATION_CATEGORY_ID = 1361051057475682537

        # Get the track validation category
        track_validation_category = channel.guild.get_channel(TRACK_VALIDATION_CATEGORY_ID)
        if not track_validation_category:
            logger.error(f"Track validation category with ID {TRACK_VALIDATION_CATEGORY_ID} not found")
            return False

        # Validate position parameter to prevent TypeError
        if position is not None and not isinstance(position, int):
            logger.warning(f"Invalid position parameter: {position} (type: {type(position)}). Using None instead.")
            position = None

        # Move the channel to the track validation category
        # Only pass position parameter if it's a valid integer
        if position is not None:
            await channel.edit(category=track_validation_category, position=position)
        else:
            await channel.edit(category=track_validation_category)

        logger.info(f"Moved channel {channel.name} to track validation category")
        return True
    except Exception as e:
        logger.error(f"Error moving channel to track validation: {e}")
        logger.error(traceback.format_exc())
        return False

def extract_subtotal_from_result(result: dict) -> float:
    """Extract subtotal from group order processing result.

    Args:
        result: The result dictionary from process_group_order

    Returns:
        float: The subtotal amount, or 0.0 if not found
    """
    try:
        # Try to get subtotal from fees data first
        if 'fees' in result and result['fees']:
            fees_data = result['fees']
            if 'subtotal' in fees_data and fees_data['subtotal'] > 0:
                subtotal = float(fees_data['subtotal'])
                logger.info(f"Extracted subtotal from fees_data: ${subtotal:.2f}")
                return subtotal

        # Try to calculate from cart items if fees data not available
        if 'cart_items' in result and result['cart_items']:
            cart_items = result['cart_items']
            calculated_subtotal = 0.0

            for item in cart_items:
                if isinstance(item, dict):
                    # If item is a dictionary with price and quantity
                    price = float(item.get('price', 0))
                    quantity = int(item.get('quantity', 1))
                    calculated_subtotal += price * quantity
                elif isinstance(item, str):
                    # If item is a formatted string, try to extract price
                    import re
                    price_match = re.search(r'\$(\d+\.?\d*)', item)
                    if price_match:
                        calculated_subtotal += float(price_match.group(1))

            logger.info(f"Calculated subtotal from cart items: ${calculated_subtotal:.2f}")
            return calculated_subtotal

        logger.warning("Could not extract subtotal from result")
        return 0.0

    except Exception as e:
        logger.error(f"Error extracting subtotal from result: {e}")
        return 0.0

def validate_subtotal_for_queue(subtotal: float) -> bool:
    """Validate if subtotal meets queue requirements ($25-$35).

    Args:
        subtotal: The subtotal amount to validate

    Returns:
        bool: True if subtotal is within valid range, False otherwise
    """
    return MIN_SUBTOTAL <= subtotal <= MAX_SUBTOTAL

class OrderTrackButton(discord.ui.View):
    def __init__(self, order_link: str):
        super().__init__()

        # Create a blue (primary) style button
        track_button = discord.ui.Button(
            label="🔎 Track Order",
            style=discord.ButtonStyle.primary,  # This makes it blue
            url=order_link
        )
        self.add_item(track_button)

class PaymentMethodSelectView(discord.ui.View):
    """View for selecting payment method used for CervPay."""

    def __init__(self, total_amount: float, original_user: discord.User):
        super().__init__(timeout=300)  # 5 minute timeout
        self.total_amount = total_amount
        self.original_user = original_user

    @discord.ui.select(
        placeholder="Select the payment method you used...",
        options=[
            discord.SelectOption(
                label="Zelle (**********)",
                value="zelle",
                description="Zelle payment to **********",
                emoji="📱"
            ),
            discord.SelectOption(
                label="Venmo (@CervMethod)",
                value="venmo",
                description="Venmo payment to @CervMethod (F&F)",
                emoji="💚"
            ),
            discord.SelectOption(
                label="PayPal (@ItsCerv)",
                value="paypal",
                description="PayPal payment to @ItsCerv (F&F)",
                emoji="💙"
            ),
            discord.SelectOption(
                label="Cryptocurrency",
                value="crypto",
                description="Cryptocurrency payment",
                emoji="🪙"
            ),
            discord.SelectOption(
                label="Card/Cashapp/Bank",
                value="card",
                description="Card, Cashapp, or Bank payment",
                emoji="🏦"
            )
        ]
    )
    async def payment_method_select(self, interaction: discord.Interaction, select: discord.ui.Select):
        """Handle payment method selection."""
        try:
            # Check if the user who clicked is the same as who clicked "Paid"
            if interaction.user.id != self.original_user.id:
                await interaction.response.send_message("❌ Only the person who clicked 'Paid' can select the payment method.", ephemeral=True)
                return

            # Get the selected payment method
            selected_method = select.values[0]

            # Map payment method values to display names
            method_display_names = {
                "zelle": "Zelle (**********)",
                "venmo": "Venmo (@CervMethod)",
                "paypal": "PayPal (@ItsCerv)",
                "crypto": "Cryptocurrency",
                "card": "Card/Cashapp/Bank"
            }

            selected_method_display = method_display_names.get(selected_method, selected_method)

            # Create confirmation embed with payment method (Customer field removed)
            embed = discord.Embed(
                title="✅ Payment Confirmed",
                description="Thanks! A Chef will verify and place your order shortly.",
                color=discord.Color.green()
            )
            embed.add_field(
                name="💳 Payment Method",
                value=selected_method_display,
                inline=True
            )
            embed.add_field(
                name="💰 Amount",
                value=f"${self.total_amount:.2f}",
                inline=True
            )
            embed.set_footer(text="Payment confirmation received")

            # Send confirmation message with proper Cerv ping (direct mention for guaranteed ping)
            await interaction.response.send_message(
                content=f"<@572991971359195138> Payment confirmation received!",
                embed=embed,
                ephemeral=False
            )

            # Disable the view after selection
            self.clear_items()

        except Exception as e:
            logger.error(f"Error in payment method selection: {e}")
            await interaction.response.send_message("❌ Error processing payment confirmation. Please contact support.", ephemeral=True)

    async def on_timeout(self):
        """Handle timeout - disable the view."""
        self.clear_items()

class CervPayView(discord.ui.View):
    """View for CervPay automated payment system buttons."""

    def __init__(self, total_amount: float):
        super().__init__(timeout=None)  # Persistent view
        self.total_amount = total_amount

    @discord.ui.button(label="💳 Payments", style=discord.ButtonStyle.primary, custom_id="cervpay_payments")
    async def payments_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Show payment methods when Payments button is clicked - uses same embed as /cerv command."""
        try:
            # Create payment methods embed using the exact same structure as /cerv command
            embed = discord.Embed(
                title="💰 Cerv's Payment Methods",
                description=f"Please send **${self.total_amount:.2f}** using one of the payment options below:",
                color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple (same as /cerv)
            )

            # Add a thumbnail for a modern look (same as /cerv)
            embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057669368119360/newtap_to_pay.gif?ex=6879f4fb&is=6878a37b&hm=59024acf496badef3f93f3857a16fc5450a6de306240b281a22515e2193b2a5b&=")  # Payment icon

            # Digital payments (recommended) - exact same as /cerv
            embed.add_field(
                name="💳 Digital payments (recommended)",
                value="**Zelle:** `**********`\n**Venmo:** `@CervMethod` *(F&F)*\n**PayPal:** `@ItsCerv` *(F&F)*",
                inline=False
            )

            # Cryptocurrency - exact same as /cerv
            embed.add_field(
                name="🪙 Cryptocurrency",
                value="ask for crypto",
                inline=False
            )

            # Card payments (credits only) - exact same as /cerv
            embed.add_field(
                name="🏦 Card payments (credits only)",
                value="[**Pay with Card/Cashapp/Bank**](https://themethod.sellauth.com/product/credits-cerv)",
                inline=False
            )

            # Add footer with important note (same as /cerv)
            embed.set_footer(text="⚠️ IMPORTANT: Always use Friends & Family (F&F) for PayPal/Venmo payments")

            # Send as NON-ephemeral message (visible to all) - same behavior as /cerv
            await interaction.response.send_message(embed=embed, ephemeral=False)

        except Exception as e:
            logger.error(f"Error in payments button: {e}")
            await interaction.response.send_message("❌ Error displaying payment methods. Please try again.", ephemeral=True)

    @discord.ui.button(label="✅ Paid", style=discord.ButtonStyle.success, custom_id="cervpay_paid")
    async def paid_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle payment confirmation when Paid button is clicked - shows payment method selection."""
        try:
            # Create payment method selection embed
            embed = discord.Embed(
                title="💳 Payment Method Selection",
                description=f"Please select the payment method you used to send **${self.total_amount:.2f}**:",
                color=discord.Color.blue()
            )
            embed.add_field(
                name="👤 Customer",
                value=interaction.user.mention,
                inline=True
            )
            embed.add_field(
                name="💰 Amount Sent",
                value=f"${self.total_amount:.2f}",
                inline=True
            )
            embed.set_footer(text="Select your payment method from the dropdown below")

            # Create payment method selection view
            payment_select_view = PaymentMethodSelectView(self.total_amount, interaction.user)

            # Send payment method selection prompt (ephemeral to the user who clicked)
            await interaction.response.send_message(
                embed=embed,
                view=payment_select_view,
                ephemeral=True
            )

        except Exception as e:
            logger.error(f"Error in paid button: {e}")
            await interaction.response.send_message("❌ Error processing payment confirmation. Please contact support.", ephemeral=True)

async def get_session() -> aiohttp.ClientSession:
    """Get or create the global HTTP session."""
    global _http_session
    if _http_session is None or _http_session.closed:
        _http_session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=20, ttl_dns_cache=300)
        )
    return _http_session

async def get_latest_embed(channel) -> Optional[discord.Embed]:
    """Get the latest embed from a channel."""
    try:
        async for message in channel.history(limit=10):
            if message.embeds:
                return message.embeds[0]
        return None
    except Exception as e:
        logger.error(f"Error getting latest embed: {e}")
        return None

async def is_store_open(guild) -> bool:
    """Check if the store is open by examining the latest embed in the status channel."""
    try:
        status_channel = guild.get_channel(1340406893927075900)  # Status Channel
        if not status_channel:
            logger.error("Status channel not found")
            return False

        # Check channel name first (most reliable indicator)
        if "open" in status_channel.name.lower():
            return True

        # Check latest embed as fallback
        latest_embed = await get_latest_embed(status_channel)
        if latest_embed and latest_embed.title:
            # If the title contains "Open", the store is open
            return "Open" in latest_embed.title or "🟢" in latest_embed.title
        return False
    except Exception as e:
        logger.error(f"Error checking if store is open: {e}")
        return False

@bot.event
async def setup_hook():
    """Set up tasks before the bot starts."""
    # Initialize the HTTP session
    await get_session()

    # Record start time for uptime tracking
    bot.launch_time = time.time()

    # Initialize tracking tasks list
    bot.tracking_tasks = []

    # Load saved tracking data and resume tracking
    await load_tracking_data()



    # Load status embed data
    await load_status_embed_data()

    # Load credits system data
    await load_credits_data()
    await load_redeemed_keys()

    # Load CervPay system data
    await load_cervpay_data()

    # Set up payment views
    setup_payment_views(bot, command_metrics)
    logger.info("<:check:1360153501866393692> Payment views registered")

    # Initialize background tasks list (for future use)
    bot.bg_tasks = []
    logger.info("<:check:1360153501866393692> Background tasks initialized")

async def save_tracking_data():
    """Save active tracking information to a file."""
    try:
        # Create a copy of the tracking data with only serializable information
        tracking_data = {}
        for order_id, data in active_tracking.items():
            tracking_data[order_id] = {
                'channel_id': data['channel_id'],
                'start_time': data['start_time'],
                'last_status': data.get('last_status'),
                'order_link': data.get('order_link'),
                'status_embed_message_id': data.get('status_embed_message_id'),
                'status_history': data.get('status_history', []),
                'delivery_ping_sent': data.get('delivery_ping_sent', False)
            }

        # Save to file
        with open(TRACKING_DATA_FILE, 'w') as f:
            json.dump(tracking_data, f)

        logger.info(f"Saved tracking data for {len(tracking_data)} orders")
    except Exception as e:
        logger.error(f"Error saving tracking data: {e}")
        logger.error(traceback.format_exc())

async def load_tracking_data():
    """Load tracking data from file and resume tracking."""
    global active_tracking

    try:
        # Check if the file exists
        if not os.path.exists(TRACKING_DATA_FILE):
            logger.info("No tracking data file found")
            return

        # Load data from file
        with open(TRACKING_DATA_FILE, 'r') as f:
            tracking_data = json.load(f)

        if not tracking_data:
            logger.info("No tracking data to resume")
            return

        logger.info(f"Found tracking data for {len(tracking_data)} orders")

        # Resume tracking for each order
        for order_id, data in tracking_data.items():
            try:
                # Get the channel
                channel_id = data['channel_id']
                channel = bot.get_channel(int(channel_id))

                # If channel not found in cache, try fetching from API
                if not channel:
                    try:
                        channel = await bot.fetch_channel(int(channel_id))
                        logger.info(f"Successfully fetched channel {channel_id} from API for order {order_id}")
                    except discord.NotFound:
                        logger.warning(f"Channel {channel_id} not found (deleted) for order {order_id}")
                        continue
                    except discord.Forbidden:
                        logger.warning(f"No permission to access channel {channel_id} for order {order_id}")
                        continue
                    except Exception as e:
                        logger.warning(f"Error fetching channel {channel_id} for order {order_id}: {e}")
                        continue

                # Store in active tracking
                active_tracking[order_id] = {
                    'channel_id': channel_id,
                    'start_time': data['start_time'],
                    'last_status': data.get('last_status'),
                    'order_link': data.get('order_link'),
                    'status_embed_message_id': data.get('status_embed_message_id'),
                    'status_history': data.get('status_history', []),
                    'delivery_ping_sent': data.get('delivery_ping_sent', False)
                }

                # Get a session
                session = await get_session()

                # Resume tracking
                order_link = data.get('order_link', f"https://www.ubereats.com/orders/{order_id}")
                tracking_task = asyncio.create_task(
                    track_order_status(order_id, channel, session, active_tracking_dict=active_tracking, save_tracking_func=save_tracking_data)
                )

                # Store the task
                if not hasattr(bot, 'tracking_tasks'):
                    bot.tracking_tasks = []
                bot.tracking_tasks.append(tracking_task)

                # Notify in the channel
                await channel.send(f"<:check:1360153501866393692> Resumed tracking for order {order_id}")

                logger.info(f"Resumed tracking for order {order_id} in channel {channel_id}")
            except Exception as e:
                logger.error(f"Error resuming tracking for order {order_id}: {e}")
                logger.error(traceback.format_exc())
    except Exception as e:
        logger.error(f"Error loading tracking data: {e}")
        logger.error(traceback.format_exc())

@bot.event
async def on_app_command_error(interaction: discord.Interaction, error: app_commands.AppCommandError):
    """Handle app command errors."""
    # Handle errors
    logger.error(f"Command error: {error}")
    traceback.print_exception(type(error), error, error.__traceback__)

@bot.event
async def on_ready():
    """Called when the bot is ready."""
    logger.info(f"<:check:1360153501866393692> Logged in as {bot.user}")
    logger.info(f"Command mode: {'Global' if COMMANDS_GLOBAL else 'Guild-only'}")

    # Memory usage info
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    logger.info(f"Memory usage: {memory_info.rss / 1024 / 1024:.2f} MB")

    # Setup automated order system commands
    try:
        setup_order_commands(bot)
        logger.info("<:check:1360153501866393692> Automated order system commands registered")
    except Exception as e:
        logger.error(f"<:cancel:1360154555295207596> Failed to setup order commands: {e}")
        logger.error(traceback.format_exc())

    # Setup vouch tracking system commands
    try:
        vouch_tracking.setup_vouch_commands(bot)
        logger.info("<:check:1360153501866393692> Vouch tracking system commands registered")
    except Exception as e:
        logger.error(f"<:cancel:1360154555295207596> Failed to setup vouch commands: {e}")
        logger.error(traceback.format_exc())

    # Setup order control commands (closeorders/openorders)
    try:
        from .order_control_commands import setup
        await setup(bot)
        logger.info("<:check:1360153501866393692> Order control commands registered")
    except Exception as e:
        logger.error(f"<:cancel:1360154555295207596> Failed to setup order control commands: {e}")
        logger.error(traceback.format_exc())

    # Initialize order state manager and update bot status
    try:
        from .order_state_manager import order_state_manager

        # Update bot status based on current order state
        if order_state_manager.are_orders_open():
            activity = discord.Activity(
                type=discord.ActivityType.watching,
                name="for orders | Orders OPEN ✅"
            )
            status = discord.Status.online
        else:
            activity = discord.Activity(
                type=discord.ActivityType.watching,
                name="orders are CLOSED 🚫"
            )
            status = discord.Status.do_not_disturb

        await bot.change_presence(status=status, activity=activity)
        logger.info(f"<:check:1360153501866393692> Order state manager initialized - Orders {'OPEN' if order_state_manager.are_orders_open() else 'CLOSED'}")
    except Exception as e:
        logger.error(f"<:cancel:1360154555295207596> Failed to initialize order state manager: {e}")
        logger.error(traceback.format_exc())

    # PHASE 4 FIX: Setup Phase 4 completion monitor with bot instance
    try:
        from botorders.phase4_completion_monitor import phase4_monitor
        phase4_monitor.set_bot(bot)
        logger.info("<:check:1360153501866393692> Phase 4 completion monitor bot instance configured")
    except Exception as e:
        logger.error(f"<:cancel:1360154555295207596> Failed to setup Phase 4 completion monitor: {e}")
        logger.error(traceback.format_exc())

    try:
        if COMMANDS_GLOBAL:
            synced = await bot.tree.sync()
            logger.info(f"<:check:1360153501866393692> Synced {len(synced)} command(s) globally")
        else:
            bot.tree.copy_global_to(guild=GUILD)
            synced = await bot.tree.sync(guild=GUILD)
            logger.info(f"<:check:1360153501866393692> Synced {len(synced)} command(s) to guild")
    except Exception as e:
        logger.error(f"<:cancel:1360154555295207596> Failed to sync commands: {e}")
        logger.error(traceback.format_exc())

@bot.event
async def on_message(message):
    # Process commands first
    await bot.process_commands(message)

    # Skip messages from the bot itself
    if message.author == bot.user:
        return

    # Process vouch tracking
    await vouch_tracking.on_message_vouch_tracking(message)

    # Check if the message is in a channel with 'ticket' in the name and in the ticket category
    channel = message.channel
    if (isinstance(channel, discord.TextChannel) and
            'ticket' in channel.name.lower() and
            channel.category_id == TICKET_CATEGORY_ID):
        # Check if this is a successful order message (contains a group order link)
        group_link = await extract_group_link(message)
        if group_link:
            # Process the order first (let the normal flow continue)
            pass
        # Check if this is a message from a staff member with a specific command
        elif message.content.lower() == '!addtoqueue' and message.author.guild_permissions.manage_channels:
            # Move the channel to the queue
            await move_to_queue(channel)
            return

    try:
        # Extract group link
        group_link = await extract_group_link(message)
        if not group_link:
            return

        # Normalize the link to ensure consistent matching
        # Remove any trailing parameters or fragments
        if '?' in group_link:
            group_link = group_link.split('?')[0]
        if '#' in group_link:
            group_link = group_link.split('#')[0]

        # Ensure the link ends with /join for consistency
        if not group_link.endswith('/join'):
            if group_link.endswith('/'):
                group_link = group_link + 'join'
            else:
                group_link = group_link + '/join'

        logger.info(f"<:search:1360154069028835410> Group order link detected: {group_link}")

        # Send a processing message with a nice embed
        processing_message = await message.channel.send(embed=create_processing_embed())

        # Process group order
        result = await process_group_order(group_link)

        # Add the group link to the result dictionary
        if isinstance(result, dict):
            result['group_link'] = group_link

        # Handle locked/error cases
        if isinstance(result, dict) and 'error' in result:
            if result['error'].get('type') == 'LOCKED_ORDER':
                await processing_message.delete()
                await message.channel.send(embed=create_locked_order_embed())
                return

        if not result:
            await processing_message.delete()
            await message.channel.send(embed=create_locked_order_embed())
            return

        # Process order summary without promo checking or pricing calculations

        # Process cart items
        cart_items = []
        calculated_subtotal = 0

        if 'cart_items' in result:
            for item in result['cart_items']:
                price = item.get('price', 0)
                quantity = item.get('quantity', 1)
                title = item.get('title', 'Unknown Item')
                cart_items.append(f"{title} x{quantity} (${price/100:.2f})")
                calculated_subtotal += (price * quantity) / 100

        # PriceCheckerV2 Integration - Get real-time pricing analysis
        logger.info("Starting PriceCheckerV2 analysis...")
        pricing_analysis = None
        try:
            pricing_analysis = await method_bot_price_analysis(group_link)
            if pricing_analysis.get('success'):
                logger.info(f"PriceCheckerV2 analysis successful: {pricing_analysis.get('message', '')}")
            else:
                logger.warning(f"PriceCheckerV2 analysis failed: {pricing_analysis.get('error', 'Unknown error')}")
        except Exception as e:
            logger.error(f"PriceCheckerV2 analysis exception: {str(e)}")
            pricing_analysis = {'success': False, 'error': f'Analysis failed: {str(e)}'}

        # Delete the processing message
        await processing_message.delete()

        # Create and send enhanced order summary with pricing analysis
        summary_embed = create_order_summary_embed(result, cart_items, pricing_analysis)
        await message.channel.send(embed=summary_embed)

        # Send loyal customer embed if pricing analysis is successful
        if pricing_analysis and pricing_analysis.get('success'):
            pricing_data = pricing_analysis.get('pricing_comparison', {})
            pricing_breakdown = pricing_data.get('pricing_breakdown', {})

            # Calculate loyal customer price (total minus $1)
            original_subtotal = pricing_breakdown.get('subtotal', 0)
            original_taxes = pricing_breakdown.get('taxes', 0)
            method_discount = 25.00
            method_fee = 10.00

            discounted_subtotal = max(0.0, original_subtotal - method_discount)
            updated_taxes = original_taxes + method_fee
            method_total = discounted_subtotal + updated_taxes
            loyal_customer_price = method_total - 1.00

            # Create and send loyal customer embed with direct channel link
            loyal_customer_embed = create_loyal_customer_embed(loyal_customer_price)
            await message.channel.send(embed=loyal_customer_embed)

        # Send a nicely formatted success message with pricing analysis results
        if pricing_analysis and pricing_analysis.get('success'):
            pricing_data = pricing_analysis.get('pricing_comparison', {})
            price_message = pricing_analysis.get('message', '')

            # Calculate Method pricing for success message
            pricing_breakdown = pricing_data.get('pricing_breakdown', {})
            original_uber_total = pricing_breakdown.get('total', 0)

            # Apply Method pricing transformations
            original_subtotal = pricing_breakdown.get('subtotal', 0)
            method_discount = 25.00
            discounted_subtotal = max(0.0, original_subtotal - method_discount)

            original_taxes = pricing_breakdown.get('taxes', 0)
            method_fee = 10.00
            updated_taxes = original_taxes + method_fee

            method_total = discounted_subtotal + updated_taxes

            # Create pricing message showing Uber total → Method total
            method_price_message = f"${original_uber_total:.2f} → ${method_total:.2f}"

            # Check if CervPay is enabled for automated payment processing
            if cervpay_enabled:
                # CervPay enabled - show automated payment embed
                success_embed = discord.Embed(
                    title="<:check:1360153501866393692> Your order has been automatically processed",
                    description=f"**Final Total:** ${method_total:.2f}\n\n"
                               f"Please click the **Payments** button below and send **${method_total:.2f}** to one of the payment options.\n\n"
                               f"After payment, press the **'Paid'** button to proceed.",
                    color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
                )
                success_embed.set_footer(text="This is in BETA, the final price may change")

                # Create CervPay view with payment buttons
                cervpay_view = CervPayView(method_total)

                # Add a thumbnail for a modern look
                success_embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057668692705442/newfood.gif?ex=6879f4fb&is=6878a37b&hm=1e6dae283bd9828646e04b0dc1bdbeb59e3718929ec073fdaaa4163d71a54776&=")

                await message.channel.send(embed=success_embed, view=cervpay_view)
            else:
                # CervPay disabled - show original embed
                success_embed = discord.Embed(
                    title="<:check:1360153501866393692> Order Processed Successfully",
                    description=f"Your order has been processed\n\n**Pricing Analysis:** {method_price_message}\n\n**Next Steps**\nPlease wait patiently. A Chef or Waiter will assist you shortly.",
                    color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
                )

                # Add a thumbnail for a modern look
                success_embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057668692705442/newfood.gif?ex=6879f4fb&is=6878a37b&hm=1e6dae283bd9828646e04b0dc1bdbeb59e3718929ec073fdaaa4163d71a54776&=")  # Food/order icon
                success_embed.set_footer(text="Thank you for using The Method")

                await message.channel.send(embed=success_embed)

        elif pricing_analysis and not pricing_analysis.get('success'):
            # Show warning if pricing analysis failed but still processed the order
            warning_embed = discord.Embed(
                title="<:warning:1360153488629432351> Order Processed (Pricing Analysis Failed)",
                description=f"Your order has been processed, but pricing analysis failed.\n\n**Error:** {pricing_analysis.get('error', 'Unknown error')}\n\n**Next Steps**\nPlease wait patiently. A Chef or Waiter will assist you shortly.",
                color=discord.Color.from_rgb(254, 231, 92)  # Yellow
            )
            warning_embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057668692705442/newfood.gif?ex=6879f4fb&is=6878a37b&hm=1e6dae283bd9828646e04b0dc1bdbeb59e3718929ec073fdaaa4163d71a54776&=")
            warning_embed.set_footer(text="The Method | Basic processing completed")
            await message.channel.send(embed=warning_embed)

        # Automatic queue movement logic for ticket channels
        if (isinstance(channel, discord.TextChannel) and
                'ticket' in channel.name.lower() and
                channel.category_id == TICKET_CATEGORY_ID):

            # Extract subtotal from the result
            extracted_subtotal = extract_subtotal_from_result(result)
            logger.info(f"Extracted subtotal for queue validation: ${extracted_subtotal:.2f}")

            # Check if subtotal meets queue requirements ($25-$35)
            if validate_subtotal_for_queue(extracted_subtotal):
                logger.info(f"Subtotal ${extracted_subtotal:.2f} is within queue range (${MIN_SUBTOTAL}-${MAX_SUBTOTAL}), moving to queue")

                # Move channel to queue (silently without embed notification)
                queue_success = await move_to_queue(channel)
                if queue_success:
                    logger.info("Successfully moved channel to queue automatically")
                else:
                    logger.error("Failed to move channel to queue automatically")
            else:
                logger.info(f"Subtotal ${extracted_subtotal:.2f} is outside queue range (${MIN_SUBTOTAL}-${MAX_SUBTOTAL}), keeping in current location")

    except Exception as e:
        logger.error(f"Error processing group order link: {str(e)}")
        logger.error(traceback.format_exc())

        # Try to delete the processing message if it exists
        try:
            if 'processing_message' in locals():
                await processing_message.delete()
        except:
            pass

        # Send a nicely formatted error message
        await message.channel.send(embed=create_error_embed(str(e)))



@bot.tree.command(
    name="vouchtop",
    description="Display vouch leaderboard",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)

async def vouchtop_command(interaction: discord.Interaction):
    """Display the vouch leaderboard."""
    await track_command_metrics("vouchtop")(vouchtop)(interaction)

@bot.tree.command(
    name="track",
    description="Track an Uber order status",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)
@app_commands.describe(
    order_id="The Uber order ID to track"
)

async def track_command(interaction: discord.Interaction, order_id: str):
    # Track command execution time
    start_time = time.time()

    try:
        await interaction.response.defer(ephemeral=True)
        logger.info(f"📌 `/track` command triggered by {interaction.user} for order: {order_id}")

        # Construct the order link
        orderlink = f"https://www.ubereats.com/orders/{order_id}"

        # Get the HTTP session
        session = await get_session()

        # Fetch order details with the shared session using the global function
        order_details = await fetch_order_details(order_id, session)

        if order_details:
            # Extract subtotal for validation
            subtotal = 0.0
            try:
                # Try to extract subtotal from order details
                if 'subtotal' in order_details:
                    subtotal_str = order_details['subtotal']
                    # Remove currency symbol and convert to float
                    subtotal = float(re.sub(r'[^\d.]', '', subtotal_str))
                    logger.info(f"Extracted subtotal: ${subtotal:.2f}")
            except Exception as e:
                logger.error(f"Error extracting subtotal: {e}")
                logger.error(traceback.format_exc())

            # Check if subtotal meets requirements ($23-$35)
            if MIN_SUBTOTAL <= subtotal <= MAX_SUBTOTAL:
                logger.info(f"Subtotal ${subtotal:.2f} is within valid range (${MIN_SUBTOTAL}-${MAX_SUBTOTAL})")

                # Move channel to track validation category
                await move_to_track_validation(interaction.channel)
                logger.info(f"Moved channel to track validation category")
            else:
                logger.info(f"Subtotal ${subtotal:.2f} is outside valid range (${MIN_SUBTOTAL}-${MAX_SUBTOTAL})")

                # Send warning about subtotal requirements
                if subtotal < MIN_SUBTOTAL:
                    await interaction.followup.send(
                        f"⚠️ **Warning**: Order subtotal (${subtotal:.2f}) is below the minimum requirement of ${MIN_SUBTOTAL:.2f}.",
                        ephemeral=True
                    )
                elif subtotal > MAX_SUBTOTAL:
                    await interaction.followup.send(
                        f"⚠️ **Warning**: Order subtotal (${subtotal:.2f}) exceeds the maximum limit of ${MAX_SUBTOTAL:.2f}.",
                        ephemeral=True
                    )

            # Create the success embed with enhanced modern design
            order_embed = discord.Embed(
                title="<:startup:1360177289664401418> Order Tracking Initiated",
                description="Your order has been successfully placed and is now being tracked!",
                color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
            )

            # Set a nice thumbnail - using the original tracking thumbnail
            order_embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057664729088122/newphone_burger.gif?ex=6879f4fa&is=6878a37a&hm=b1a0cd125bad80a6cb31cfca8ef458120a20bcf8bfde6bfd61872880214c5a03&=")

            # Add spacing to the description
            order_embed.description += "\n\n"

            # Restaurant section with emoji (only if not unknown)
            store_name = order_details.get('store', 'Unknown Store')
            if store_name != 'Unknown Store' and store_name != 'N/A' and not ('item' in store_name.lower() or '$' in store_name):
                order_embed.add_field(
                    name="<:promotion:1360153519415361546> Restaurant",
                    value=f"**{store_name}**",
                    inline=False
                )

            # ETA section with emoji
            order_embed.add_field(
                name="<:clock:1360156495517323264> Estimated Arrival",
                value=f"**{order_details['eta']}**",
                inline=True
            )

            # Customer section with emoji
            customer_name = order_details.get('customer', 'Unknown')
            if customer_name == 'N/A' or not customer_name:
                customer_name = interaction.user.display_name

            order_embed.add_field(
                name="<:personalinformation:1360153515296559285> Customer",
                value=f"**{customer_name}**",
                inline=True
            )

            # Order items section with better formatting
            items_text = order_details['items']
            formatted_items = "\n".join([f"╰・ *{item.strip()}*" for item in items_text.split('•') if item.strip()])
            order_embed.add_field(
                name="<:shoppingcart:1360153495155642459> Order Items",
                value=formatted_items if formatted_items else "No items found",
                inline=False
            )

            # Delivery address section with code block formatting
            order_embed.add_field(
                name="<:placeholder:1360153497869488137> Delivery Address",
                value=f"```{order_details['address']}```",
                inline=False
            )

            # Order link with button-like formatting
            order_embed.add_field(
                name="<:link:1360154729002565662> Order Link",
                value=f"[**Click to view order**]({orderlink})",
                inline=False
            )

            # Add footer with tracking info and timestamp
            order_embed.set_footer(text="Order is being tracked automatically | The Method")
            order_embed.timestamp = datetime.datetime.now()

            # Create view with tracking button
            view = OrderTrackButton(orderlink)

            # Send the embed with the view and start tracking
            await interaction.followup.send(embed=order_embed, view=view)

            # Store tracking information
            active_tracking[order_id] = {
                'channel_id': interaction.channel.id,
                'start_time': time.time(),
                'last_status': None,
                'order_link': orderlink,
                'status_embed_message_id': None,
                'status_history': [],
                'delivery_ping_sent': False
            }

            # Create initial persistent tracking embed
            from themethodbot.common.bot import OrderStatusButtons

            initial_embed = discord.Embed(
                title="<:car:1360177292730568865> Order Tracking",
                description=f"**Order ID:** `{order_id}`\n**Status:** Initializing tracking...",
                color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
            )

            # Add queue position if channel is in queue
            queue_position = await get_queue_position(interaction.channel)
            if queue_position:
                total_queue = await get_total_queue_count()
                initial_embed.add_field(
                    name="📊 Queue Position",
                    value=f"#{queue_position} of {total_queue}",
                    inline=True
                )

            initial_embed.add_field(
                name="📋 Status History",
                value="No status updates yet.",
                inline=False
            )

            initial_embed.set_footer(text="The Method | Order Tracking")
            initial_embed.timestamp = discord.utils.utcnow()

            # Create view with Status History button
            view_buttons = OrderStatusButtons(order_id, active_tracking)

            # Send initial tracking embed and store message ID
            initial_message = await interaction.followup.send(embed=initial_embed, view=view_buttons)
            active_tracking[order_id]['status_embed_message_id'] = initial_message.id

            # Save tracking data to file
            await save_tracking_data()

            # Start tracking in a background task and store it for cleanup
            logger.info(f"THEMETHODBOT: Creating tracking task for order_id: {order_id}")
            logger.info(f"Channel: {interaction.channel.name if hasattr(interaction.channel, 'name') else 'Unknown channel'}")
            logger.info(f"Session: {session}")

            try:
                # Use the global track_order_status function
                tracking_task = asyncio.create_task(track_order_status(order_id, interaction.channel, session, active_tracking_dict=active_tracking, save_tracking_func=save_tracking_data))
                if not hasattr(bot, 'tracking_tasks'):
                    bot.tracking_tasks = []
                bot.tracking_tasks.append(tracking_task)
                logger.info(f"Successfully created and stored tracking task for order_id: {order_id}")
            except Exception as e:
                logger.error(f"Error creating tracking task: {e}")
                logger.error(traceback.format_exc())
                await interaction.followup.send(f"⚠️ Error starting order tracking: {str(e)}")
        else:
            await interaction.followup.send("<:cancel:1360154555295207596> Failed to fetch order details.", ephemeral=True)
            logger.error(f"Failed to fetch order details for order ID: {order_id}")

        # Track metrics
        execution_time = time.time() - start_time
        if "track" not in command_metrics:
            command_metrics["track"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["track"]['count'] += 1
        command_metrics["track"]['total_time'] += execution_time
        command_metrics["track"]['max_time'] = max(
            command_metrics["track"]['max_time'],
            execution_time
        )

        logger.debug(f"Command track executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"Error in track command: {e}")
        logger.error(traceback.format_exc())
        if not interaction.response.is_done():
            await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)

@bot.tree.command(name="open",
    description="Opens the store."
)
@app_commands.checks.has_any_role(1340830489719734443)  # Only allow the new role

async def open_store_slash(interaction: discord.Interaction):
    """Slash command to open the store."""
    await track_command_metrics("open_store")(open_store)(interaction)

@bot.tree.command(
    name="close",
    description="Closes the store."
)
@app_commands.guilds(GUILD)  # Fixed
@app_commands.checks.has_any_role(1340830489719734443)  # Staff role check

async def close_store_slash(interaction: discord.Interaction):
    """Slash command to close the store."""
    await track_command_metrics("close_store")(close_store)(interaction)



@bot.tree.command(
    name="cerv",
    description="Cerv's payment details.",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)

async def cerv_command(interaction: discord.Interaction):
    await track_command_metrics("cerv")(cerv)(interaction)


@bot.tree.command(
    name="nelo",
    description="Nelo's payment details.",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)

async def nelo_command(interaction: discord.Interaction):
    await track_command_metrics("nelo")(nelo)(interaction)

@bot.tree.command(
    name="glitchyz",
    description="Glitchyz's payment details.",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)

async def glitchyz_command(interaction: discord.Interaction):
    await track_command_metrics("glitchyz")(Glitchyz)(interaction)



@bot.tree.command(
    name="sync",
    description="Sync slash commands (admin only)",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(administrator=True)
async def sync_command(interaction: discord.Interaction):
    """Manually sync slash commands."""
    try:
        await interaction.response.defer(ephemeral=True)

        if COMMANDS_GLOBAL:
            synced = await bot.tree.sync()
            await interaction.followup.send(f"✅ Synced {len(synced)} command(s) globally")
        else:
            bot.tree.copy_global_to(guild=GUILD)
            synced = await bot.tree.sync(guild=GUILD)
            await interaction.followup.send(f"✅ Synced {len(synced)} command(s) to guild")

    except Exception as e:
        logger.error(f"Error syncing commands: {e}")
        logger.error(traceback.format_exc())
        await interaction.followup.send(f"❌ Error syncing commands: {e}")

@bot.tree.command(
    name="storenotinpromo",
    description="Shows how to find eligible stores with promos",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)

async def storenotinpromo(interaction: discord.Interaction):
    """Shows instructions for finding eligible stores with promos."""
    # Track command execution time
    start_time = time.time()

    try:
        # Check if we have this in cache
        cache_key = "storenotinpromo_embed"
        if cache_key in data_cache:
            logger.debug("Using cached storenotinpromo embed")
            embed = data_cache[cache_key]
        else:
            # Create the embed
            embed = discord.Embed(
                title="<:search:1360154069028835410> Find Eligible Stores",
                description="**The store you're looking for isn't in our promo. Follow these steps to find eligible restaurants:**",
                color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
            )

            # Add a thumbnail for a modern look
            embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057668692705442/newfood.gif?ex=6879f4fb&is=6878a37b&hm=1e6dae283bd9828646e04b0dc1bdbeb59e3718929ec073fdaaa4163d71a54776&=")  # Search/store icon

            # Add spacing to the description
            embed.description += "\n\n"

            # Step 1
            embed.add_field(
                name="🔸 Step 1: Open the Promo Link",
                value="Open [**this link**](https://tinyurl.com/TheMethodUE) in Chrome or Safari",
                inline=False
            )

            # Step 2
            embed.add_field(
                name="🔹 Step 2: Enter Your Address",
                value="Log in to Uber Eats and enter your delivery address",
                inline=False
            )

            # Step 3
            embed.add_field(
                name="🔸 Step 3: Open the Promo Link Again in a NEW tab",
                value="Open [**the same link**](https://tinyurl.com/TheMethodUE) in a second tab",
                inline=False
            )

            # Step 4
            embed.add_field(
                name="🔹 Step 4: Browse Eligible Restaurants",
                value="You'll now see all restaurants eligible for our promo!",
                inline=False
            )

            # Add a footer with a tip
            embed.set_footer(text="Tip: If you still don't see eligible stores, try opening the link in a new tab again")

            # Add the screenshot as a smaller image at the bottom
            embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1349998616110043237/image.png?width=600&height=338")

            # Cache the embed for future use
            data_cache[cache_key] = embed
            logger.debug("Cached storenotinpromo embed")

        await interaction.response.send_message(embed=embed)

        # Track metrics
        execution_time = time.time() - start_time
        if "storenotinpromo" not in command_metrics:
            command_metrics["storenotinpromo"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["storenotinpromo"]['count'] += 1
        command_metrics["storenotinpromo"]['total_time'] += execution_time
        command_metrics["storenotinpromo"]['max_time'] = max(
            command_metrics["storenotinpromo"]['max_time'],
            execution_time
        )

        logger.debug(f"Command storenotinpromo executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"<:cancel:1360154555295207596> Error in storenotinpromo command: {e}")
        logger.error(traceback.format_exc())
        if not interaction.response.is_done():
            await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)





@bot.tree.command(
    name="testapi",
    description="Test the Uber Eats API connection",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True)
@app_commands.describe(
    orderlink="Optional order link to test (if not provided, will only test cookie)"
)
async def testapi_slash(interaction: discord.Interaction, orderlink: str = None):
    """Test the Uber Eats API connection."""
    await interaction.response.defer(ephemeral=True)

    try:
        # Get the HTTP session
        session = await get_session()

        # Log the UBER_COOKIE value (first 50 chars)
        from themethodbot.common.bot import UBER_COOKIE
        cookie_preview = UBER_COOKIE[:50] + "..." if UBER_COOKIE else "None"
        logger.info(f"UBER_COOKIE preview: {cookie_preview}")

        # Create a test embed
        embed = discord.Embed(
            title="API Test Results",
            color=discord.Color.blue()
        )

        embed.add_field(
            name="Cookie Status",
            value=f"{'✅ Cookie found' if UBER_COOKIE else '❌ Cookie not found'}\n(Preview: `{cookie_preview}`)",
            inline=False
        )

        # If an order link was provided, test fetching the order
        if orderlink:
            await interaction.followup.send("Testing API with provided order link...", ephemeral=True)

            # Extract order ID
            order_id = re.search(r"orders/([a-f0-9-]+)", orderlink)
            if order_id:
                order_id = order_id.group(1)

                # Fetch order details
                from themethodbot.common.bot import fetch_order_details
                order_details = await fetch_order_details(order_id, session)

                if order_details:
                    embed.add_field(
                        name="API Test Result",
                        value="✅ Successfully fetched order details!",
                        inline=False
                    )

                    # Add some order details
                    embed.add_field(
                        name="Store Name",
                        value=f"`{order_details['store_name']}`",
                        inline=True
                    )

                    embed.add_field(
                        name="Delivery Status",
                        value=f"`{order_details['delivery_status']}`",
                        inline=True
                    )

                    embed.add_field(
                        name="Order Items",
                        value="\n".join([f"╰・ *{item}*" for item in order_details['cart_items'][:3]]) +
                              ("\n╰・ *...and more*" if len(order_details['cart_items']) > 3 else ""),
                        inline=False
                    )
                else:
                    embed.add_field(
                        name="API Test Result",
                        value="❌ Failed to fetch order details. Check logs for more information.",
                        inline=False
                    )
            else:
                embed.add_field(
                    name="API Test Result",
                    value="❌ Invalid order link format.",
                    inline=False
                )

        # Send the embed
        await interaction.followup.send(embed=embed, ephemeral=True)

    except Exception as e:
        logger.error(f"Error in testapi command: {e}")
        logger.error(traceback.format_exc())
        await interaction.followup.send(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)

@bot.tree.command(
    name="ordersuccess",
    description="Track a successful order using order link",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)
@app_commands.describe(
    orderlink="The order link to track"
)

async def ordersuccess_slash(
    interaction: discord.Interaction,
    orderlink: str
):
    """Slash command to track a successful order."""
    # Track command execution time
    start_time = time.time()

    try:
        logger.info(f"📌 `/ordersuccess` was triggered by {interaction.user} with order link: {orderlink}")
        await interaction.response.defer()

        # Handle channel renaming
        current_channel = interaction.channel
        if current_channel.name.startswith(("nelo4317-", "itscerv-", "_glitchyz-", "ticket-")):
            try:
                ticket_number = re.sub(r"^(nelo4317-|itscerv-|_glitchyz-|ticket-)", "", current_channel.name)
                new_name = f"{ticket_number}-delivering"
                await current_channel.edit(name=new_name)
                logger.info(f"<:check:1360153501866393692> Successfully renamed channel to {new_name}")

                # Move the channel to the delivering category
                await move_to_delivering(current_channel)
            except Exception as e:
                logger.error(f"<:cancel:1360154555295207596> Failed to rename channel: {str(e)}")
                logger.error(traceback.format_exc())

        # Extract order ID and fetch details
        order_id = re.search(r"orders/([a-f0-9-]+)", orderlink)
        if order_id:
            order_id = order_id.group(1)

            # Get the HTTP session
            session = await get_session()

            # Fetch order details with the shared session using the global function
            order_details = await fetch_order_details(order_id, session)

            if order_details:
                # Create the success embed with enhanced modern design
                order_embed = discord.Embed(
                    title="<:startup:1360177289664401418> Order Tracking Initiated",
                    description="Your order has been successfully placed and is now being tracked!",
                    color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
                )

                # Set a nice thumbnail - using the original tracking thumbnail
                order_embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057664729088122/newphone_burger.gif?ex=6879f4fa&is=6878a37a&hm=b1a0cd125bad80a6cb31cfca8ef458120a20bcf8bfde6bfd61872880214c5a03&=")

                # Add spacing to the description
                order_embed.description += "\n\n"

                # Restaurant section with emoji (only if not unknown)
                store_name = order_details.get('store', 'Unknown Store')
                if store_name != 'Unknown Store' and store_name != 'N/A' and not ('item' in store_name.lower() or '$' in store_name):
                    order_embed.add_field(
                        name="<:promotion:1360153519415361546> Restaurant",
                        value=f"**{store_name}**",
                        inline=False
                    )

                # ETA section with emoji
                order_embed.add_field(
                    name="<:clock:1360156495517323264> Estimated Arrival",
                    value=f"**{order_details['eta']}**",
                    inline=True
                )

                # Customer section with emoji
                customer_name = order_details.get('customer', 'Unknown')
                if customer_name == 'N/A' or not customer_name:
                    customer_name = interaction.user.display_name

                order_embed.add_field(
                    name="<:personalinformation:1360153515296559285> Customer",
                    value=f"**{customer_name}**",
                    inline=True
                )

                # Add spacing between sections
                # No divider needed

                # Order items section with better formatting
                items_text = order_details['items']
                formatted_items = "\n".join([f"╰・ *{item.strip()}*" for item in items_text.split('•') if item.strip()])
                order_embed.add_field(
                    name="<:shoppingcart:1360153495155642459> Order Items",
                    value=formatted_items if formatted_items else "No items found",
                    inline=False
                )

                # Add spacing between sections
                # No divider needed

                # Delivery address section with code block formatting
                order_embed.add_field(
                    name="<:placeholder:1360153497869488137> Delivery Address",
                    value=f"```{order_details['address']}```",
                    inline=False
                )

                # Order link with button-like formatting
                order_embed.add_field(
                    name="<:link:1360154729002565662> Order Link",
                    value=f"[**Click to view order**]({orderlink})",
                    inline=False
                )

                # Add footer with tracking info and timestamp
                order_embed.set_footer(text="Order is being tracked automatically | The Method")
                order_embed.timestamp = datetime.datetime.now()

                # Create view with tracking button
                view = OrderTrackButton(orderlink)

                # Send the embed with the view and start tracking
                await interaction.followup.send(embed=order_embed, view=view)

                # Store tracking information
                active_tracking[order_id] = {
                    'channel_id': interaction.channel.id,
                    'start_time': time.time(),
                    'last_status': None,
                    'order_link': orderlink,
                    'status_embed_message_id': None,
                    'status_history': [],
                    'delivery_ping_sent': False
                }

                # Create initial persistent tracking embed
                from themethodbot.common.bot import OrderStatusButtons

                initial_embed = discord.Embed(
                    title="<:car:1360177292730568865> Order Tracking",
                    description=f"**Order ID:** `{order_id}`\n**Status:** Initializing tracking...",
                    color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
                )

                # Add queue position if channel is in queue
                queue_position = await get_queue_position(interaction.channel)
                if queue_position:
                    total_queue = await get_total_queue_count()
                    initial_embed.add_field(
                        name="📊 Queue Position",
                        value=f"#{queue_position} of {total_queue}",
                        inline=True
                    )

                initial_embed.add_field(
                    name="📋 Status History",
                    value="No status updates yet.",
                    inline=False
                )

                initial_embed.set_footer(text="The Method | Order Tracking")
                initial_embed.timestamp = discord.utils.utcnow()

                # Create view with Status History button
                view_buttons = OrderStatusButtons(order_id, active_tracking)

                # Send initial tracking embed and store message ID
                initial_message = await interaction.followup.send(embed=initial_embed, view=view_buttons)
                active_tracking[order_id]['status_embed_message_id'] = initial_message.id

                # Save tracking data to file
                await save_tracking_data()

                # Start tracking in a background task and store it for cleanup
                logger.info(f"THEMETHODBOT: Creating tracking task for order_id: {order_id}")
                logger.info(f"Channel: {interaction.channel.name if hasattr(interaction.channel, 'name') else 'Unknown channel'}")
                logger.info(f"Session: {session}")

                try:
                    # Use the global track_order_status function
                    tracking_task = asyncio.create_task(track_order_status(order_id, interaction.channel, session, active_tracking_dict=active_tracking, save_tracking_func=save_tracking_data))
                    if not hasattr(bot, 'tracking_tasks'):
                        bot.tracking_tasks = []
                    bot.tracking_tasks.append(tracking_task)
                    logger.info(f"Successfully created and stored tracking task for order_id: {order_id}")
                except Exception as e:
                    logger.error(f"Error creating tracking task: {e}")
                    logger.error(traceback.format_exc())
                    await interaction.followup.send(f"⚠️ Error starting order tracking: {str(e)}")
            else:
                await interaction.followup.send("<:cancel:1360154555295207596> Failed to fetch order details.", ephemeral=True)
        else:
            await interaction.followup.send("<:cancel:1360154555295207596> Invalid order link format.", ephemeral=True)
            return

        logger.info("<:check:1360153501866393692> Order tracking initiated.")

        # Track metrics
        execution_time = time.time() - start_time
        if "ordersuccess" not in command_metrics:
            command_metrics["ordersuccess"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["ordersuccess"]['count'] += 1
        command_metrics["ordersuccess"]['total_time'] += execution_time
        command_metrics["ordersuccess"]['max_time'] = max(
            command_metrics["ordersuccess"]['max_time'],
            execution_time
        )

        logger.debug(f"Command ordersuccess executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"Error in ordersuccess command: {e}")
        logger.error(traceback.format_exc())
        try:
            if interaction.response.is_done():
                await interaction.followup.send(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
            else:
                await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
        except Exception:
            pass



@bot.tree.command(
    name="cleardelivered",
    description="Close all channels with 'delivered' in the name in the specified category",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.checks.has_any_role(1340213865748762634)  # Staff role check
@app_commands.describe()
@app_commands.checks.cooldown(1, 30, key=lambda i: i.user.id)  # 1 use per 30 seconds per user
async def cleardelivered_command(interaction: discord.Interaction):
    """Close all channels with 'delivered' in the name in the specified category."""
    # Track command execution time
    start_time = time.time()

    try:
        await interaction.response.defer(ephemeral=True)
        logger.info(f"📌 `/cleardelivered` command triggered by {interaction.user}")

        # Get the guild and category
        guild = interaction.guild
        category_id = 1354242418211422419
        category = guild.get_channel(category_id)

        if not category:
            await interaction.followup.send(f"<:cancel:1360154555295207596> Category with ID {category_id} not found.", ephemeral=True)
            return

        # Find all channels in the category with "delivered" in the name
        delivered_channels = [channel for channel in category.channels
                             if isinstance(channel, discord.TextChannel) and "delivered" in channel.name.lower()]

        if not delivered_channels:
            await interaction.followup.send("<:check:1360153501866393692> No channels with 'delivered' in the name found in the category.", ephemeral=True)
            return

        # Check if batch size is safe for Discord interaction timeout (15 minutes = 900 seconds)
        estimated_time = (len(delivered_channels) - 1) * 30 + (len(delivered_channels) * 5)  # 30s delay + 5s processing per channel
        max_safe_time = 800  # 13 minutes 20 seconds for safety margin

        if estimated_time > max_safe_time:
            await interaction.followup.send(
                f"⚠️ **Large Batch Warning**\n"
                f"Found {len(delivered_channels)} channels (estimated {estimated_time//60:.0f}m {estimated_time%60:.0f}s to process).\n"
                f"This may exceed Discord's interaction timeout. Consider processing in smaller batches.\n"
                f"**Recommended**: Process ≤20 channels at a time for reliability.\n\n"
                f"The command will continue but may lose interaction capability after 15 minutes.",
                ephemeral=True
            )

        # Send initial status message
        await interaction.followup.send(
            f"<:startup:1360177289664401418> Found {len(delivered_channels)} channels with 'delivered' in the name. Starting cleanup process...\n"
            f"⏱️ Estimated time: {estimated_time//60:.0f}m {estimated_time%60:.0f}s (30s delay between channels)",
            ephemeral=True
        )

        # Process channels sequentially to avoid rate limits
        processed_count = 0
        failed_count = 0
        failed_channels = []
        interaction_expired = False

        # Process each channel one at a time
        for i, channel in enumerate(delivered_channels):
            try:
                # Safe interaction message sending with timeout handling
                async def safe_followup_send(message, ephemeral=True):
                    nonlocal interaction_expired
                    if not interaction_expired:
                        try:
                            await interaction.followup.send(message, ephemeral=ephemeral)
                        except (discord.NotFound, discord.HTTPException) as e:
                            if "Unknown interaction" in str(e) or "already been acknowledged" in str(e):
                                interaction_expired = True
                                logger.warning(f"Discord interaction expired, continuing with logging only")
                            else:
                                raise

                # Update status message
                await safe_followup_send(f"🔄 Processing channel: {channel.name} ({processed_count+1}/{len(delivered_channels)})")

                # Send $delete command directly using bot's message sending capability
                await channel.send("$delete")
                logger.info(f"Sent $delete command to channel {channel.name} ({channel.id})")

                # Successfully processed this channel
                processed_count += 1
                await safe_followup_send(f"<:check:1360153501866393692> Successfully processed channel: {channel.name}")

                # Wait 30 seconds between $delete messages (except for the last channel)
                if i < len(delivered_channels) - 1:  # Don't wait after the last channel
                    await safe_followup_send(f"⏳ Waiting 30 seconds before processing next channel to avoid rate limits...")
                    logger.info(f"Waiting 30 seconds before processing next channel ({i+2}/{len(delivered_channels)})")
                    await asyncio.sleep(30)

            except Exception as e:
                logger.error(f"Error processing channel {channel.name}: {str(e)}")
                logger.error(traceback.format_exc())
                failed_count += 1
                failed_channels.append(channel.name)
                await safe_followup_send(f"<:cancel:1360154555295207596> Error processing channel {channel.name}: {str(e)}")
                await asyncio.sleep(2)  # Wait before continuing to next channel

        # Send completion message
        completion_message = ""
        if failed_count > 0:
            completion_message = (
                f"<:check:1360153501866393692> Processed {processed_count}/{len(delivered_channels)} channels successfully. "
                f"Failed to process {failed_count} channels: {', '.join(failed_channels)}"
            )
        else:
            completion_message = f"<:check:1360153501866393692> Successfully processed all {processed_count} channels with 'delivered' in the name."

        if interaction_expired:
            completion_message += f"\n⚠️ Discord interaction expired during processing, but all operations completed successfully."

        # Try to send completion message, but don't fail if interaction expired
        try:
            if not interaction_expired:
                await interaction.followup.send(completion_message, ephemeral=True, delete_after=10)
            else:
                logger.info(f"Command completed: {completion_message}")
        except (discord.NotFound, discord.HTTPException):
            logger.info(f"Command completed (interaction expired): {completion_message}")

        # Track metrics
        execution_time = time.time() - start_time
        if "cleardelivered" not in command_metrics:
            command_metrics["cleardelivered"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["cleardelivered"]['count'] += 1
        command_metrics["cleardelivered"]['total_time'] += execution_time
        command_metrics["cleardelivered"]['max_time'] = max(
            command_metrics["cleardelivered"]['max_time'],
            execution_time
        )

        logger.debug(f"Command cleardelivered executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"Error in cleardelivered command: {e}")
        logger.error(traceback.format_exc())
        try:
            if interaction.response.is_done():
                await interaction.followup.send(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
            else:
                await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
        except (discord.NotFound, discord.HTTPException) as interaction_error:
            if "Unknown interaction" in str(interaction_error) or "already been acknowledged" in str(interaction_error):
                logger.error(f"Could not send error message due to expired interaction: {e}")
            else:
                logger.error(f"Failed to send error message: {interaction_error}")

@bot.tree.command(
    name="addtoqueue",
    description="Add the current channel to the queue",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.checks.has_any_role(1340213865748762634)  # Staff role check
async def addtoqueue_command(interaction: discord.Interaction):
    """Slash command to add the current channel to the queue."""
    await track_command_metrics("addtoqueue")(addtoqueue)(interaction)

async def addtoqueue(interaction: discord.Interaction):
    """Add the current channel to the queue."""
    # Track command execution time
    start_time = time.time()

    try:
        # Check if user is authorized
        authorized_users = [572991971359195138, 542140310524788736, 965501524304343082]
        staff_role = discord.utils.get(interaction.guild.roles, id=1340213865748762634)
        if staff_role not in interaction.user.roles and interaction.user.id not in authorized_users:
            await interaction.response.send_message("⛔ You don't have permission to use this command.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)
        logger.info(f"📌 `/addtoqueue` command triggered by {interaction.user} in channel {interaction.channel.name}")

        # Move the channel to the queue
        success = await move_to_queue(interaction.channel)

        if success:
            await interaction.followup.send("<:check:1360153501866393692> Channel added to queue successfully!", ephemeral=True)
        else:
            await interaction.followup.send("<:cancel:1360154555295207596> Failed to add channel to queue.", ephemeral=True)

        # Track metrics
        execution_time = time.time() - start_time
        if "addtoqueue" not in command_metrics:
            command_metrics["addtoqueue"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["addtoqueue"]['count'] += 1
        command_metrics["addtoqueue"]['total_time'] += execution_time
        command_metrics["addtoqueue"]['max_time'] = max(
            command_metrics["addtoqueue"]['max_time'],
            execution_time
        )

        logger.debug(f"Command addtoqueue executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"Error in addtoqueue command: {e}")
        logger.error(traceback.format_exc())
        if not interaction.response.is_done():
            await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
        else:
            await interaction.followup.send(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)

@bot.tree.command(
    name="cleardelivering",
    description="Close all channels with 'delivering' in the name in the specified category",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.checks.has_any_role(1340213865748762634)  # Staff role check
@app_commands.describe()
@app_commands.checks.cooldown(1, 30, key=lambda i: i.user.id)  # 1 use per 30 seconds per user
async def cleardelivering_command(interaction: discord.Interaction):
    """Close all channels with 'delivering' in the name in the specified category."""
    # Track command execution time
    start_time = time.time()

    try:
        await interaction.response.defer(ephemeral=True)
        logger.info(f"📌 `/cleardelivering` command triggered by {interaction.user}")

        # Get the guild and category
        guild = interaction.guild
        category_id = 1354242418211422419
        category = guild.get_channel(category_id)

        if not category:
            await interaction.followup.send(f"<:cancel:1360154555295207596> Category with ID {category_id} not found.", ephemeral=True)
            return

        # Find all channels in the category with "delivering" in the name
        delivering_channels = [channel for channel in category.channels
                             if isinstance(channel, discord.TextChannel) and "delivering" in channel.name.lower()]

        if not delivering_channels:
            await interaction.followup.send("<:check:1360153501866393692> No channels with 'delivering' in the name found in the category.", ephemeral=True)
            return

        # Check if batch size is safe for Discord interaction timeout (15 minutes = 900 seconds)
        estimated_time = (len(delivering_channels) - 1) * 30 + (len(delivering_channels) * 5)  # 30s delay + 5s processing per channel
        max_safe_time = 800  # 13 minutes 20 seconds for safety margin

        if estimated_time > max_safe_time:
            await interaction.followup.send(
                f"⚠️ **Large Batch Warning**\n"
                f"Found {len(delivering_channels)} channels (estimated {estimated_time//60:.0f}m {estimated_time%60:.0f}s to process).\n"
                f"This may exceed Discord's interaction timeout. Consider processing in smaller batches.\n"
                f"**Recommended**: Process ≤20 channels at a time for reliability.\n\n"
                f"The command will continue but may lose interaction capability after 15 minutes.",
                ephemeral=True
            )

        # Send initial status message
        await interaction.followup.send(
            f"<:startup:1360177289664401418> Found {len(delivering_channels)} channels with 'delivering' in the name. Starting cleanup process...\n"
            f"⏱️ Estimated time: {estimated_time//60:.0f}m {estimated_time%60:.0f}s (30s delay between channels)",
            ephemeral=True
        )

        # Process channels sequentially to avoid rate limits
        processed_count = 0
        failed_count = 0
        failed_channels = []
        interaction_expired = False

        # Process each channel one at a time
        for i, channel in enumerate(delivering_channels):
            try:
                # Safe interaction message sending with timeout handling
                async def safe_followup_send(message, ephemeral=True):
                    nonlocal interaction_expired
                    if not interaction_expired:
                        try:
                            await interaction.followup.send(message, ephemeral=ephemeral)
                        except (discord.NotFound, discord.HTTPException) as e:
                            if "Unknown interaction" in str(e) or "already been acknowledged" in str(e):
                                interaction_expired = True
                                logger.warning(f"Discord interaction expired, continuing with logging only")
                            else:
                                raise

                # Update status message
                await safe_followup_send(f"🔄 Processing channel: {channel.name} ({processed_count+1}/{len(delivering_channels)})")

                # Send $delete command directly using bot's message sending capability
                await channel.send("$delete")
                logger.info(f"Sent $delete command to channel {channel.name} ({channel.id})")

                # Successfully processed this channel
                processed_count += 1
                await safe_followup_send(f"<:check:1360153501866393692> Successfully processed channel: {channel.name}")

                # Wait 30 seconds between $delete messages (except for the last channel)
                if i < len(delivering_channels) - 1:  # Don't wait after the last channel
                    await safe_followup_send(f"⏳ Waiting 30 seconds before processing next channel to avoid rate limits...")
                    logger.info(f"Waiting 30 seconds before processing next channel ({i+2}/{len(delivering_channels)})")
                    await asyncio.sleep(30)

            except Exception as e:
                logger.error(f"Error processing channel {channel.name}: {str(e)}")
                logger.error(traceback.format_exc())
                failed_count += 1
                failed_channels.append(channel.name)
                await safe_followup_send(f"<:cancel:1360154555295207596> Error processing channel {channel.name}: {str(e)}")
                await asyncio.sleep(2)  # Wait before continuing to next channel

        # Send completion message
        completion_message = ""
        if failed_count > 0:
            completion_message = (
                f"<:check:1360153501866393692> Processed {processed_count}/{len(delivering_channels)} channels successfully. "
                f"Failed to process {failed_count} channels: {', '.join(failed_channels)}"
            )
        else:
            completion_message = f"<:check:1360153501866393692> Successfully processed all {processed_count} channels with 'delivering' in the name."

        if interaction_expired:
            completion_message += f"\n⚠️ Discord interaction expired during processing, but all operations completed successfully."

        # Try to send completion message, but don't fail if interaction expired
        try:
            if not interaction_expired:
                await interaction.followup.send(completion_message, ephemeral=True, delete_after=10)
            else:
                logger.info(f"Command completed: {completion_message}")
        except (discord.NotFound, discord.HTTPException):
            logger.info(f"Command completed (interaction expired): {completion_message}")

        # Track metrics
        execution_time = time.time() - start_time
        if "cleardelivering" not in command_metrics:
            command_metrics["cleardelivering"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["cleardelivering"]['count'] += 1
        command_metrics["cleardelivering"]['total_time'] += execution_time
        command_metrics["cleardelivering"]['max_time'] = max(
            command_metrics["cleardelivering"]['max_time'],
            execution_time
        )

        logger.debug(f"Command cleardelivering executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"Error in cleardelivering command: {e}")
        logger.error(traceback.format_exc())
        try:
            if interaction.response.is_done():
                await interaction.followup.send(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
            else:
                await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
        except (discord.NotFound, discord.HTTPException) as interaction_error:
            if "Unknown interaction" in str(interaction_error) or "already been acknowledged" in str(interaction_error):
                logger.error(f"Could not send error message due to expired interaction: {e}")
            else:
                logger.error(f"Failed to send error message: {interaction_error}")


    except Exception as e:
        logger.error(f"Error in cleardelivered command: {e}")
        logger.error(traceback.format_exc())
        if interaction.response.is_done():
            await interaction.followup.send(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
        else:
            await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)

# Add a metrics command to show performance statistics
@bot.tree.command(
    name="metrics",
    description="Show bot performance metrics",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.checks.has_any_role(1340213865748762634)  # Staff role check
async def metrics_command(interaction: discord.Interaction):
    """Show bot performance metrics."""
    try:
        # Get memory usage
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        memory_usage = memory_info.rss / 1024 / 1024  # MB

        # Get CPU usage
        cpu_percent = process.cpu_percent(interval=0.5)

        # Create embed with modern design
        embed = discord.Embed(
            title="📊 Bot Performance Dashboard",
            description=f"Real-time performance statistics for **{bot.user.name}**",
            color=discord.Color.from_rgb(91, 141, 238)  # Light Blue
        )

        # Calculate uptime in a more readable format
        uptime_seconds = time.time() - bot.launch_time
        hours, remainder = divmod(uptime_seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        uptime_str = f"{int(hours)}h {int(minutes)}m {int(seconds)}s"

        # Add system metrics with icons and better formatting
        embed.add_field(
            name="💻 System Resources",
            value=f"**💾 Memory:** `{memory_usage:.2f} MB`\n"
                  f"**🔌 CPU Load:** `{cpu_percent:.1f}%`\n"
                  f"**⏱️ Uptime:** `{uptime_str}`",
            inline=False
        )

        # Add command metrics
        if command_metrics:
            # Sort by count
            sorted_commands = sorted(
                command_metrics.items(),
                key=lambda x: x[1]['count'],
                reverse=True
            )

            commands_value = ""
            for i, (cmd_name, metrics) in enumerate(sorted_commands[:10], 1):  # Show top 10 with ranking
                avg_time = metrics['total_time'] / metrics['count']
                # Format with modern code blocks and better spacing
                commands_value += f"**#{i}** `/{cmd_name}` • {metrics['count']} calls\n"
                commands_value += f"┣ Avg: `{avg_time*1000:.1f}ms` • Max: `{metrics['max_time']*1000:.1f}ms`\n\n"

            embed.add_field(
                name="� Command Usage Stats (Top 10)",
                value=commands_value or "*No commands executed yet.*",
                inline=False
            )
        else:
            embed.add_field(
                name="� Command Usage Stats",
                value="*No commands executed yet.*",
                inline=False
            )

        # Add spacing between sections
        # No divider needed

        # Add background tasks info with better formatting
        tasks_info = ""

        # Background tasks
        if hasattr(bot, 'bg_tasks'):
            running_tasks = sum(1 for t in bot.bg_tasks if not t.done())
            tasks_info += f"**🔄 Background Processes:** `{running_tasks}/{len(bot.bg_tasks)} active`\n"

            # Only show details if there are tasks
            if len(bot.bg_tasks) > 0:
                tasks_info += "\n"
                for i, task in enumerate(bot.bg_tasks):
                    status = "🟢 Running" if not task.done() else "🔴 Completed"
                    tasks_info += f"`Process #{i+1}:` {status}\n"

        # Order tracking tasks
        if hasattr(bot, 'tracking_tasks'):
            active_tracking = sum(1 for t in bot.tracking_tasks if not t.done())
            tasks_info += f"\n**📰 Order Tracking:** `{active_tracking}/{len(bot.tracking_tasks)} active`\n"

        embed.add_field(
            name="<:clock:1360156495517323264> Active Processes",
            value=tasks_info or "*No background tasks running.*",
            inline=False
        )

        # Add footer with timestamp
        embed.set_footer(text=f"Generated at {datetime.datetime.now().strftime('%H:%M:%S')} | The Method")

        await interaction.response.send_message(embed=embed, ephemeral=True)
    except Exception as e:
        logger.error(f"Error in metrics command: {e}")
        logger.error(traceback.format_exc())
        await interaction.response.send_message(f"<:cancel:1360154555295207596> Error: {e}", ephemeral=True)

# Command error handler
@bot.event
async def on_command_error(ctx, error):
    """Handle command errors."""
    if isinstance(error, commands.CommandOnCooldown):
        await ctx.send(f"⏳ Command on cooldown. Try again in {error.retry_after:.1f} seconds.")
    elif isinstance(error, commands.MissingPermissions):
        await ctx.send("⛔ You don't have permission to use this command.")
    else:
        logger.error(f"Command error: {error}")
        logger.error(traceback.format_exc())
        await ctx.send(f"<:cancel:1360154555295207596> An error occurred: {error}")

# Command error handler - simplified version without command queue

# Application command error handler
@bot.tree.error
async def on_app_command_error(interaction: discord.Interaction, error: app_commands.AppCommandError):
    """Handle application command errors."""
    if isinstance(error, app_commands.CommandOnCooldown):
        # Simply inform the user about the cooldown without queueing
        command_name = interaction.command.name if interaction.command else "unknown"

        # Provide specific message for bulk channel management commands
        if command_name in ["cleardelivered", "cleardelivering"]:
            message = (
                f"⏳ **Bulk Channel Management Cooldown**\n"
                f"The `/{command_name}` command is on cooldown to prevent rate limiting.\n"
                f"Please wait **{error.retry_after:.1f} seconds** before using this command again.\n\n"
                f"*This cooldown helps protect the bot from Discord API rate limits.*"
            )
        else:
            message = f"⏳ Command `/{command_name}` is on cooldown. Please try again in {error.retry_after:.1f} seconds."

        await interaction.response.send_message(message, ephemeral=True)
    elif isinstance(error, app_commands.MissingPermissions):
        await interaction.response.send_message(
            "⛔ You don't have permission to use this command.",
            ephemeral=True
        )
    else:
        logger.error(f"App command error: {error}")
        logger.error(traceback.format_exc())

        # Try to respond if not already responded
        try:
            if interaction.response.is_done():
                await interaction.followup.send(
                    f"<:cancel:1360154555295207596> An error occurred: {error}",
                    ephemeral=True
                )
            else:
                await interaction.response.send_message(
                    f"<:cancel:1360154555295207596> An error occurred: {error}",
                    ephemeral=True
                )
        except Exception as e:
            logger.error(f"Failed to send error message: {e}")

# Command metrics decorator
def track_command_metrics(command_name):
    """Decorator to track command execution metrics."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                return await func(*args, **kwargs)
            finally:
                execution_time = time.time() - start_time
                if command_name not in command_metrics:
                    command_metrics[command_name] = {
                        'count': 0,
                        'total_time': 0,
                        'max_time': 0
                    }

                command_metrics[command_name]['count'] += 1
                command_metrics[command_name]['total_time'] += execution_time
                command_metrics[command_name]['max_time'] = max(
                    command_metrics[command_name]['max_time'],
                    execution_time
                )

                logger.debug(
                    f"Command {command_name} executed in {execution_time:.4f}s "
                    f"(avg: {command_metrics[command_name]['total_time'] / command_metrics[command_name]['count']:.4f}s)"
                )
        return wrapper
    return decorator

# Credits System Commands

@bot.tree.command(
    name="redeemcredits",
    description="Redeem a serial key for credits",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.describe(serial_key="The serial key to redeem")
@app_commands.default_permissions(send_messages=True, use_application_commands=True)
async def redeemcredits_command(interaction: discord.Interaction, serial_key: str):
    """Redeem a serial key for credits."""
    try:
        await interaction.response.defer(ephemeral=True)

        # Clean and validate the serial key
        clean_key = serial_key.strip().upper()

        if not clean_key:
            await interaction.followup.send("❌ Please provide a valid serial key.", ephemeral=True)
            return

        # Check if key is already redeemed
        if await is_key_redeemed(clean_key):
            await interaction.followup.send("❌ This serial key has already been redeemed.", ephemeral=True)
            return

        # Validate the serial key and get credit amount
        credit_amount = validate_serial_key(clean_key)

        if credit_amount is None:
            await interaction.followup.send("❌ Invalid serial key. Please check your key and try again.", ephemeral=True)
            return

        # Add credits to user's balance
        user_id = str(interaction.user.id)
        new_balance = await add_user_credits(user_id, float(credit_amount))

        # Mark key as redeemed
        await mark_key_redeemed(clean_key, user_id, credit_amount)

        # Create success embed
        embed = discord.Embed(
            title="✅ Credits Redeemed Successfully!",
            color=discord.Color.green(),
            timestamp=datetime.datetime.now()
        )

        embed.add_field(
            name="💰 Credits Added",
            value=format_currency(credit_amount),
            inline=True
        )

        embed.add_field(
            name="💳 New Balance",
            value=format_currency(new_balance),
            inline=True
        )

        embed.add_field(
            name="🔑 Serial Key",
            value=f"`{clean_key}`",
            inline=False
        )

        embed.set_footer(text="TheMethod Credits System")

        await interaction.followup.send(embed=embed, ephemeral=True)

        # Log the redemption
        logger.info(f"User {interaction.user.id} ({interaction.user.name}) redeemed key {clean_key} for ${credit_amount}")

        # Log to credits logging channel
        await log_credits_event("redemption", interaction.user, amount=float(credit_amount), serial_key=clean_key, balance=new_balance)

    except Exception as e:
        logger.error(f"Error in redeemcredits command: {e}")
        # Log error to credits logging channel
        await log_credits_event("error", interaction.user, error=str(e))
        await interaction.followup.send(f"❌ An error occurred while redeeming credits: {e}", ephemeral=True)

@bot.tree.command(
    name="credits",
    description="Check your credit balance",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True)
async def credits_command(interaction: discord.Interaction):
    """Check user's credit balance."""
    try:
        await interaction.response.defer(ephemeral=True)

        # Get user's credit balance
        user_id = str(interaction.user.id)
        balance = await get_user_credits(user_id)

        # Create balance embed
        embed = discord.Embed(
            title="💳 Your Credit Balance",
            color=discord.Color.blue(),
            timestamp=datetime.datetime.now()
        )

        embed.add_field(
            name="💰 Current Balance",
            value=format_currency(balance),
            inline=False
        )

        if balance > 0:
            embed.add_field(
                name="ℹ️ Info",
                value="Use your credits for orders and services!",
                inline=False
            )
        else:
            embed.add_field(
                name="ℹ️ Info",
                value="You don't have any credits yet. Use `/redeemcredits` to add credits with a serial key!",
                inline=False
            )

        embed.set_footer(text="TheMethod Credits System")

        await interaction.followup.send(embed=embed, ephemeral=True)

    except Exception as e:
        logger.error(f"Error in credits command: {e}")
        await interaction.followup.send(f"❌ An error occurred while checking credits: {e}", ephemeral=True)

@bot.tree.command(
    name="creditcheck",
    description="Check another user's credit balance (Admin only)",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.describe(user="The user whose credits you want to check")
@app_commands.checks.has_any_role(1340213865748762634)  # Admin role check
async def creditcheck_command(interaction: discord.Interaction, user: discord.Member):
    """Check another user's credit balance (Admin only)."""
    try:
        await interaction.response.defer(ephemeral=True)

        # Get target user's credit balance
        target_user_id = str(user.id)
        balance = await get_user_credits(target_user_id)

        # Create balance embed
        embed = discord.Embed(
            title="🔍 Credit Balance Check",
            color=discord.Color.orange(),
            timestamp=datetime.datetime.now()
        )

        embed.add_field(
            name="👤 User",
            value=f"{user.mention} ({user.display_name})",
            inline=False
        )

        embed.add_field(
            name="💰 Credit Balance",
            value=format_currency(balance),
            inline=False
        )

        embed.add_field(
            name="🆔 User ID",
            value=f"`{user.id}`",
            inline=False
        )

        embed.set_footer(text="TheMethod Credits System - Admin Check")

        await interaction.followup.send(embed=embed, ephemeral=True)

        # Log the admin check
        logger.info(f"Admin {interaction.user.id} ({interaction.user.name}) checked credits for user {user.id} ({user.name}): ${balance}")

        # Log to credits logging channel
        await log_credits_event("balance_check", user, admin=interaction.user, balance=balance)

    except Exception as e:
        logger.error(f"Error in creditcheck command: {e}")
        # Log error to credits logging channel
        await log_credits_event("error", user, error=str(e))
        await interaction.followup.send(f"❌ An error occurred while checking user credits: {e}", ephemeral=True)

@bot.tree.command(
    name="removecredits",
    description="Remove credits from a user's balance (Admin only)",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.describe(
    user="The user to remove credits from",
    amount="The amount of credits to remove"
)
@app_commands.checks.has_any_role(1340213865748762634)  # Admin role check
async def removecredits_command(interaction: discord.Interaction, user: discord.Member, amount: float):
    """Remove credits from a user's balance (Admin only)."""
    try:
        await interaction.response.defer(ephemeral=True)

        # Validate amount
        if amount <= 0:
            await interaction.followup.send("❌ Amount must be greater than zero.", ephemeral=True)
            return

        # Get target user's current credit balance
        target_user_id = str(user.id)
        current_balance = await get_user_credits(target_user_id)

        # Check if user has any credits
        if current_balance == 0:
            await interaction.followup.send(
                f"❌ {user.mention} has no credits to remove (current balance: {format_currency(0)})",
                ephemeral=True
            )
            return

        # Check if user has sufficient credits
        if amount > current_balance:
            await interaction.followup.send(
                f"❌ Cannot remove {format_currency(amount)} from {user.mention}. "
                f"User only has {format_currency(current_balance)} available.",
                ephemeral=True
            )
            return

        # Calculate new balance
        new_balance = current_balance - amount

        # Update user's credit balance
        user_credits[target_user_id] = new_balance
        await save_credits_data()

        # Create success embed
        embed = discord.Embed(
            title="💳 Credits Removed Successfully",
            color=discord.Color.red(),
            timestamp=datetime.datetime.now()
        )

        embed.add_field(
            name="👤 Target User",
            value=f"{user.mention} ({user.display_name})",
            inline=False
        )

        embed.add_field(
            name="💰 Credits Removed",
            value=format_currency(amount),
            inline=True
        )

        embed.add_field(
            name="💳 New Balance",
            value=format_currency(new_balance),
            inline=True
        )

        embed.add_field(
            name="👮 Admin",
            value=f"{interaction.user.mention} ({interaction.user.display_name})",
            inline=False
        )

        embed.add_field(
            name="🆔 User ID",
            value=f"`{user.id}`",
            inline=True
        )

        embed.add_field(
            name="📅 Action Date",
            value=f"<t:{int(datetime.datetime.now().timestamp())}:F>",
            inline=True
        )

        embed.set_footer(text="TheMethod Credits System - Admin Action")

        await interaction.followup.send(embed=embed, ephemeral=True)

        # Log the credit removal for audit trail
        logger.info(
            f"ADMIN CREDIT REMOVAL: Admin {interaction.user.id} ({interaction.user.name}) "
            f"removed {format_currency(amount)} from user {user.id} ({user.name}). "
            f"Previous balance: {format_currency(current_balance)}, "
            f"New balance: {format_currency(new_balance)}"
        )

        # Log to credits logging channel
        await log_credits_event("credit_removal", user, admin=interaction.user, amount=amount, balance=new_balance)

    except Exception as e:
        logger.error(f"Error in removecredits command: {e}")
        # Log error to credits logging channel
        await log_credits_event("error", user, error=str(e))
        await interaction.followup.send(f"❌ An error occurred while removing credits: {e}", ephemeral=True)

@bot.tree.command(
    name="cervpay",
    description="Toggle the automated CervPay payment system (Admin only)",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.checks.has_any_role(1357843021139542086, 1340213865748762634)  # Both admin role IDs
async def cervpay_command(interaction: discord.Interaction):
    """Toggle the automated CervPay payment system."""
    try:
        await interaction.response.defer(ephemeral=True)

        # Toggle the CervPay state
        global cervpay_enabled
        cervpay_enabled = not cervpay_enabled

        # Save the new state
        await save_cervpay_data()

        # Create status embed
        embed = discord.Embed(
            title="🔧 CervPay System Toggle",
            color=discord.Color.green() if cervpay_enabled else discord.Color.red()
        )

        status_text = "**ENABLED** ✅" if cervpay_enabled else "**DISABLED** ❌"
        embed.add_field(
            name="Current Status",
            value=status_text,
            inline=False
        )

        if cervpay_enabled:
            embed.add_field(
                name="📋 What's Changed",
                value="• Order processing embeds now show automated payment options\n"
                      "• Users will see 'Payments' and 'Paid' buttons\n"
                      "• Payment confirmations will ping Cerv automatically",
                inline=False
            )
            embed.add_field(
                name="⚠️ Note",
                value="This is in BETA - final prices may change",
                inline=False
            )
        else:
            embed.add_field(
                name="📋 What's Changed",
                value="• Order processing embeds reverted to original format\n"
                      "• No automated payment buttons will be shown\n"
                      "• Standard 'Next Steps' message will be displayed",
                inline=False
            )

        embed.add_field(
            name="👮 Admin",
            value=f"{interaction.user.mention} ({interaction.user.display_name})",
            inline=False
        )

        embed.set_footer(text=f"CervPay toggled by {interaction.user.display_name}")

        await interaction.followup.send(embed=embed, ephemeral=True)

        # Log the toggle action
        logger.info(f"CervPay toggled to {cervpay_enabled} by {interaction.user} ({interaction.user.id})")

    except Exception as e:
        logger.error(f"Error in cervpay command: {e}")
        logger.error(traceback.format_exc())
        try:
            if interaction.response.is_done():
                await interaction.followup.send(f"❌ An error occurred while toggling CervPay: {e}", ephemeral=True)
            else:
                await interaction.response.send_message(f"❌ An error occurred while toggling CervPay: {e}", ephemeral=True)
        except Exception:
            logger.error("Failed to send error message for cervpay command")

# Graceful shutdown
async def cleanup():
    """Clean up resources before shutdown."""
    logger.info("Cleaning up resources...")

    # Save tracking data before shutdown
    await save_tracking_data()
    logger.info("Saved tracking data for resuming on next startup")



    # Save credits data before shutdown
    await save_credits_data()
    await save_redeemed_keys()
    await save_cervpay_data()
    logger.info("Saved credits system and CervPay data for resuming on next startup")

    # Cancel background tasks
    if hasattr(bot, 'bg_tasks'):
        for task in bot.bg_tasks:
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

    # Cancel tracking tasks
    if hasattr(bot, 'tracking_tasks'):
        for task in bot.tracking_tasks:
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

    # Close HTTP session
    global _http_session
    if _http_session and not _http_session.closed:
        await _http_session.close()
        logger.info("HTTP session closed")

def run_bot():
    try:
        # Run the bot with proper signal handling
        bot.run(DISCORD_BOT_TOKEN, log_handler=None)
    except KeyboardInterrupt:
        logger.info("Bot stopped by keyboard interrupt")
    except Exception as e:
        logger.error(f"Error running bot: {e}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    run_bot()