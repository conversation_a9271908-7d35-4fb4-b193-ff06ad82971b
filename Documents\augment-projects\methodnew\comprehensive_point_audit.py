"""
Comprehensive Point Audit System
Scans Discord channel for the last 7 days to audit and correct user point totals
based on their complete image posting history.
"""

import discord
import asyncio
import os
from dotenv import load_dotenv
import datetime
from typing import Dict, List, Tuple
import logging
from collections import defaultdict

# Import the existing vouch tracking system
from themethodbot.vouch_tracking import add_vouch_point, get_user_points

# Load environment variables
load_dotenv()

DISCORD_BOT_TOKEN = os.getenv("DISCORD_BOT_TOKEN")
VOUCH_CHANNEL_ID = 1340392172691918848

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('comprehensive_point_audit.log', encoding='utf-8')
    ]
)

logger = logging.getLogger('comprehensive_point_audit')

class PointAuditResults:
    """Class to track comprehensive audit results."""
    
    def __init__(self):
        self.total_users_processed = 0
        self.total_messages_scanned = 0
        self.total_images_found = 0
        self.total_points_awarded = 0
        self.user_data = defaultdict(lambda: {
            'username': 'Unknown',
            'total_images': 0,
            'current_points_before': 0,
            'points_awarded': 0,
            'final_points': 0,
            'discrepancy_found': False
        })
        self.errors = []
        self.start_time = datetime.datetime.now()
    
    def update_user_data(self, user_id: int, username: str, total_images: int, 
                        current_points: int, points_awarded: int, final_points: int):
        """Update user data in the audit results."""
        self.user_data[user_id].update({
            'username': username,
            'total_images': total_images,
            'current_points_before': current_points,
            'points_awarded': points_awarded,
            'final_points': final_points,
            'discrepancy_found': points_awarded > 0
        })
        self.total_points_awarded += points_awarded
        if total_images > 0:
            self.total_users_processed += 1
    
    def add_error(self, error: str):
        """Add an error to the results."""
        self.errors.append(error)
        logger.error(error)
    
    def print_comprehensive_report(self):
        """Print a detailed audit report."""
        end_time = datetime.datetime.now()
        duration = end_time - self.start_time
        
        print("\n" + "="*80)
        print("🔍 COMPREHENSIVE POINT AUDIT REPORT")
        print("="*80)
        print(f"📅 Audit Period: Last 7 days (168 hours)")
        print(f"📍 Channel ID: {VOUCH_CHANNEL_ID}")
        print(f"⏱️  Audit Duration: {duration.total_seconds():.2f} seconds")
        print(f"📊 Messages Scanned: {self.total_messages_scanned}")
        print(f"🖼️  Total Images Found: {self.total_images_found}")
        print(f"👥 Users with Images: {self.total_users_processed}")
        print(f"⭐ Points Awarded (Corrections): {self.total_points_awarded}")
        print(f"❌ Errors Encountered: {len(self.errors)}")
        
        # Users with discrepancies
        users_with_discrepancies = [
            (uid, data) for uid, data in self.user_data.items() 
            if data['discrepancy_found']
        ]
        
        print(f"🔧 Users with Point Discrepancies: {len(users_with_discrepancies)}")
        
        if self.user_data:
            print("\n" + "-"*80)
            print("👥 DETAILED USER BREAKDOWN")
            print("-"*80)
            
            # Sort users by total images (descending)
            sorted_users = sorted(
                [(uid, data) for uid, data in self.user_data.items() if data['total_images'] > 0],
                key=lambda x: x[1]['total_images'],
                reverse=True
            )
            
            for user_id, data in sorted_users:
                username = data['username']
                total_images = data['total_images']
                current_before = data['current_points_before']
                points_awarded = data['points_awarded']
                final_points = data['final_points']
                discrepancy = data['discrepancy_found']
                
                status = "🔧 CORRECTED" if discrepancy else "✅ CORRECT"
                
                print(f"\n👤 {username} (ID: {user_id}) - {status}")
                print(f"   🖼️  Total Images Found: {total_images}")
                print(f"   📊 Points Before Audit: {current_before}")
                print(f"   ⭐ Points Awarded: {points_awarded}")
                print(f"   🏆 Final Point Total: {final_points}")
                
                if discrepancy:
                    print(f"   📈 Discrepancy: Had {current_before}, should have {total_images}, awarded {points_awarded}")
        
        if users_with_discrepancies:
            print("\n" + "-"*80)
            print("🔧 POINT CORRECTIONS SUMMARY")
            print("-"*80)
            for user_id, data in users_with_discrepancies:
                username = data['username']
                points_awarded = data['points_awarded']
                print(f"• {username}: +{points_awarded} points")
        
        if self.errors:
            print("\n" + "-"*80)
            print("❌ ERRORS ENCOUNTERED")
            print("-"*80)
            for i, error in enumerate(self.errors, 1):
                print(f"{i}. {error}")
        
        print("\n" + "="*80)
        print("✅ COMPREHENSIVE AUDIT COMPLETE")
        print("="*80)

async def perform_comprehensive_audit():
    """Perform comprehensive point audit of the vouch channel."""
    # Set up bot intents
    intents = discord.Intents.default()
    intents.messages = True
    intents.guilds = True
    intents.message_content = True
    
    # Create bot client
    client = discord.Client(intents=intents)
    
    # Initialize results tracker
    results = PointAuditResults()
    
    @client.event
    async def on_ready():
        logger.info(f"✅ Logged in as {client.user}")
        
        try:
            # Get the vouch channel
            channel = client.get_channel(VOUCH_CHANNEL_ID)
            if not channel:
                error_msg = f"❌ Could not find channel with ID {VOUCH_CHANNEL_ID}"
                results.add_error(error_msg)
                await client.close()
                return
            
            logger.info(f"📍 Found channel: #{channel.name}")
            
            # Calculate cutoff time (7 days ago)
            cutoff_time = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(days=7)
            logger.info(f"📅 Scanning messages since: {cutoff_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
            
            # Perform comprehensive audit
            await audit_channel_messages(channel, cutoff_time, results)
            
        except Exception as e:
            error_msg = f"❌ Critical error during audit: {str(e)}"
            results.add_error(error_msg)
        
        finally:
            # Print results and close
            results.print_comprehensive_report()
            await client.close()
    
    # Run the bot
    await client.start(DISCORD_BOT_TOKEN)

async def audit_channel_messages(channel: discord.TextChannel, cutoff_time: datetime.datetime, results: PointAuditResults):
    """Audit all messages in the channel and correct point discrepancies."""
    logger.info("🔍 Starting comprehensive message audit...")

    # Dictionary to track image counts per user
    user_image_counts = defaultdict(int)
    user_names = {}

    try:
        # Phase 1: Count all images per user
        logger.info("📊 Phase 1: Counting images per user...")
        async for message in channel.history(limit=None, after=cutoff_time):
            try:
                results.total_messages_scanned += 1

                # Skip bot messages
                if message.author.bot:
                    continue

                # Store username
                user_names[message.author.id] = message.author.display_name or message.author.name

                # Count image attachments
                image_attachments = [
                    att for att in message.attachments
                    if att.content_type and att.content_type.startswith('image/')
                ]

                if image_attachments:
                    image_count = len(image_attachments)
                    user_image_counts[message.author.id] += image_count
                    results.total_images_found += image_count

                    logger.debug(f"📸 Found {image_count} image(s) from {message.author}")

                # Progress update every 100 messages
                if results.total_messages_scanned % 100 == 0:
                    logger.info(f"📊 Progress: {results.total_messages_scanned} messages scanned, {len(user_image_counts)} users with images")

            except Exception as e:
                error_msg = f"Error processing message {message.id}: {str(e)}"
                results.add_error(error_msg)
                continue

        logger.info(f"📊 Phase 1 Complete: Found {results.total_images_found} images from {len(user_image_counts)} users")

        # Phase 2: Audit and correct points for each user
        logger.info("🔧 Phase 2: Auditing and correcting points...")
        for user_id, total_images in user_image_counts.items():
            try:
                username = user_names.get(user_id, f"User {user_id}")

                # Get current points
                current_points = get_user_points(user_id)

                # Calculate discrepancy
                points_needed = total_images
                points_to_award = max(0, points_needed - current_points)

                logger.info(f"👤 Auditing {username}: {total_images} images, {current_points} current points, need {points_to_award} more")

                # Award missing points
                points_awarded = 0
                if points_to_award > 0:
                    for i in range(points_to_award):
                        try:
                            new_total = add_vouch_point(user_id)
                            points_awarded += 1
                            logger.debug(f"   ⭐ Awarded point {i+1}/{points_to_award} to {username} (new total: {new_total})")
                        except Exception as e:
                            error_msg = f"Failed to award point to {username} (ID: {user_id}): {str(e)}"
                            results.add_error(error_msg)
                            break

                # Get final points after corrections
                final_points = get_user_points(user_id)

                # Update results
                results.update_user_data(
                    user_id, username, total_images, current_points, points_awarded, final_points
                )

                if points_awarded > 0:
                    logger.info(f"✅ Corrected {username}: awarded {points_awarded} points (final total: {final_points})")
                else:
                    logger.info(f"✅ {username}: already has correct points ({current_points})")

            except Exception as e:
                error_msg = f"Error auditing user {user_id}: {str(e)}"
                results.add_error(error_msg)
                continue

    except Exception as e:
        error_msg = f"Error during message iteration: {str(e)}"
        results.add_error(error_msg)

    logger.info(f"🏁 Comprehensive audit complete. Processed {len(user_image_counts)} users, awarded {results.total_points_awarded} points")

if __name__ == "__main__":
    print("🚀 Starting Comprehensive Point Audit...")
    print("📅 Scanning last 7 days (168 hours) for complete audit")
    print(f"📍 Target Channel ID: {VOUCH_CHANNEL_ID}")
    print("🔍 This will ensure all users have correct points based on their complete posting history")
    print("-" * 80)

    try:
        asyncio.run(perform_comprehensive_audit())
    except KeyboardInterrupt:
        print("\n⚠️  Audit interrupted by user")
    except Exception as e:
        print(f"\n❌ Critical error: {e}")
        logger.error(f"Critical error in main: {e}")

    print("\n🔚 Comprehensive Point Audit finished")
